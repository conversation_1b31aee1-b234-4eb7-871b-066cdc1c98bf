<?php

namespace Tests\Feature;

use App\Models\Product;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Enums\TaxonomyType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductTaxonomyTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $category;
    protected $subcategory;
    protected $tag;
    protected $brand;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test taxonomies
        $this->category = Taxonomy::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'type' => TaxonomyType::Category->value,
            'description' => 'Electronic products',
        ]);

        $this->subcategory = Taxonomy::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $this->category->id,
            'description' => 'Smart phones',
        ]);

        $this->tag = Taxonomy::create([
            'name' => 'Featured',
            'slug' => 'featured',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Featured products',
        ]);

        $this->brand = Taxonomy::create([
            'name' => 'Apple',
            'slug' => 'apple',
            'type' => 'brand',
            'description' => 'Apple products',
        ]);
    }

    public function test_product_can_be_associated_with_categories()
    {
        $product = Product::factory()->create();

        $product->attachTaxonomies([$this->category]);

        $this->assertTrue($product->hasTaxonomies([$this->category]));
        $this->assertCount(1, $product->taxonomies);
        $this->assertEquals($this->category->id, $product->taxonomies->first()->id);
    }

    public function test_product_can_be_associated_with_multiple_taxonomies()
    {
        $product = Product::factory()->create();

        $product->attachTaxonomies([$this->category, $this->tag, $this->brand]);

        $this->assertTrue($product->hasTaxonomies([$this->category]));
        $this->assertTrue($product->hasTaxonomies([$this->tag]));
        $this->assertTrue($product->hasTaxonomies([$this->brand]));
        $this->assertCount(3, $product->taxonomies);
    }

    public function test_product_can_get_taxonomies_by_type()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->tag, $this->brand]);

        $categories = $product->taxonomiesOfType(TaxonomyType::Category);
        $tags = $product->taxonomiesOfType(TaxonomyType::Tag);
        $brands = $product->taxonomiesOfType('brand');

        $this->assertCount(1, $categories);
        $this->assertCount(1, $tags);
        $this->assertCount(1, $brands);
        $this->assertEquals($this->category->id, $categories->first()->id);
        $this->assertEquals($this->tag->id, $tags->first()->id);
        $this->assertEquals($this->brand->id, $brands->first()->id);
    }

    public function test_product_can_detach_taxonomies()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->tag]);

        $this->assertCount(2, $product->taxonomies);

        $product->detachTaxonomies([$this->category]);

        $this->assertCount(1, $product->fresh()->taxonomies);
        $this->assertFalse($product->fresh()->hasTaxonomies([$this->category]));
        $this->assertTrue($product->fresh()->hasTaxonomies([$this->tag]));
    }

    public function test_product_can_sync_taxonomies()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->tag]);

        $this->assertCount(2, $product->taxonomies);

        $product->syncTaxonomies([$this->brand]);

        $this->assertCount(1, $product->fresh()->taxonomies);
        $this->assertFalse($product->fresh()->hasTaxonomies([$this->category]));
        $this->assertFalse($product->fresh()->hasTaxonomies([$this->tag]));
        $this->assertTrue($product->fresh()->hasTaxonomies([$this->brand]));
    }

    public function test_product_can_toggle_taxonomies()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category]);

        $this->assertTrue($product->hasTaxonomies([$this->category]));

        $product->toggleTaxonomies([$this->category]);

        $this->assertFalse($product->fresh()->hasTaxonomies([$this->category]));

        $product->toggleTaxonomies([$this->category]);

        $this->assertTrue($product->fresh()->hasTaxonomies([$this->category]));
    }

    public function test_can_query_products_by_taxonomy_type()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        $product1->attachTaxonomies([$this->category]);
        $product2->attachTaxonomies([$this->tag]);
        $product3->attachTaxonomies([$this->brand]);

        $productsWithCategories = Product::withTaxonomyType(TaxonomyType::Category)->get();
        $productsWithTags = Product::withTaxonomyType(TaxonomyType::Tag)->get();
        $productsWithBrands = Product::withTaxonomyType('brand')->get();

        $this->assertCount(1, $productsWithCategories);
        $this->assertCount(1, $productsWithTags);
        $this->assertCount(1, $productsWithBrands);
        $this->assertEquals($product1->id, $productsWithCategories->first()->id);
        $this->assertEquals($product2->id, $productsWithTags->first()->id);
        $this->assertEquals($product3->id, $productsWithBrands->first()->id);
    }

    public function test_can_query_products_by_taxonomy_slug()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        $product1->attachTaxonomies([$this->category]);
        $product2->attachTaxonomies([$this->tag]);

        $productsWithElectronics = Product::withTaxonomySlug('electronics')->get();
        $productsWithFeatured = Product::withTaxonomySlug('featured')->get();

        $this->assertCount(1, $productsWithElectronics);
        $this->assertCount(1, $productsWithFeatured);
        $this->assertEquals($product1->id, $productsWithElectronics->first()->id);
        $this->assertEquals($product2->id, $productsWithFeatured->first()->id);
    }

    public function test_can_query_products_with_any_taxonomies()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        $product1->attachTaxonomies([$this->category]);
        $product2->attachTaxonomies([$this->tag]);
        // product3 has no taxonomies

        $productsWithAnyTaxonomy = Product::withAnyTaxonomies([$this->category, $this->tag])->get();

        $this->assertCount(2, $productsWithAnyTaxonomy);
        $this->assertTrue($productsWithAnyTaxonomy->contains($product1));
        $this->assertTrue($productsWithAnyTaxonomy->contains($product2));
        $this->assertFalse($productsWithAnyTaxonomy->contains($product3));
    }

    public function test_can_query_products_with_all_taxonomies()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        $product1->attachTaxonomies([$this->category, $this->tag]);
        $product2->attachTaxonomies([$this->category]);
        $product3->attachTaxonomies([$this->tag]);

        $productsWithAllTaxonomies = Product::withAllTaxonomies([$this->category, $this->tag])->get();

        $this->assertCount(1, $productsWithAllTaxonomies);
        $this->assertEquals($product1->id, $productsWithAllTaxonomies->first()->id);
    }

    public function test_product_has_taxonomy_type_check()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->tag]);

        $this->assertTrue($product->hasTaxonomyType(TaxonomyType::Category));
        $this->assertTrue($product->hasTaxonomyType(TaxonomyType::Tag));
        $this->assertFalse($product->hasTaxonomyType('brand'));
    }

    public function test_product_categories_relationship_works()
    {
        $product = Product::factory()->create();
        $product->attachTaxonomies([$this->category, $this->subcategory, $this->tag]);

        $categories = $product->categories()->get();

        $this->assertCount(2, $categories);
        $this->assertTrue($categories->contains($this->category));
        $this->assertTrue($categories->contains($this->subcategory));
        $this->assertFalse($categories->contains($this->tag));
    }

    public function test_hierarchical_taxonomy_queries_work()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        $product1->attachTaxonomies([$this->category]);
        $product2->attachTaxonomies([$this->subcategory]);

        // Test hierarchy queries
        $productsInElectronicsHierarchy = Product::withTaxonomyHierarchy($this->category->id)->get();

        $this->assertCount(2, $productsInElectronicsHierarchy);
        $this->assertTrue($productsInElectronicsHierarchy->contains($product1));
        $this->assertTrue($productsInElectronicsHierarchy->contains($product2));
    }
}
