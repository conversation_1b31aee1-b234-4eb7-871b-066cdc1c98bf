# OAuth Manager Configuration

# Google Drive OAuth Configuration
GOOGLE_DRIVE_CLIENT_ID=your-google-client-id
GOOGLE_DRIVE_CLIENT_SECRET=your-google-client-secret

# OneDrive OAuth Configuration
ONEDRIVE_CLIENT_ID=your-microsoft-client-id
ONEDRIVE_CLIENT_SECRET=your-microsoft-client-secret

# Use fwd.host service for local OAuth development
# Set to true for local development (dominio.test)
# Set to false for staging/production
GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true
ONEDRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true

# Optional: Custom redirect URIs (if not using dynamic generation)
# GOOGLE_DRIVE_REDIRECT_URI=https://your-domain.com/oauth-manager/callback/your-service-slug
# ONEDRIVE_REDIRECT_URI=https://your-domain.com/oauth-manager/callback/onedrive


OAUTH_MANAGER_DEFAULT_REDIRECT_URL=/admin