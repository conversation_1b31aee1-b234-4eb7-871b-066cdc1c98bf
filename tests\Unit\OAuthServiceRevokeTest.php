<?php

namespace LBCDev\OAuthManager\Tests\Unit;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class OAuthServiceRevokeTest extends TestCase
{
    use RefreshDatabase;

    public function test_revoke_token_returns_false_when_no_access_token()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => null,
            'refresh_token' => 'some_refresh_token',
        ]);

        $result = $service->revokeToken();

        $this->assertFalse($result);
    }

    public function test_revoke_token_calls_provider_and_clears_tokens_on_success()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_access_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->addHour(),
            'last_used_at' => now(),
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service->setProvider($mock);

        $result = $service->revokeToken();

        $this->assertTrue($result);

        // Verificar que los tokens fueron limpiados
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
        $this->assertNull($service->expires_at);
        $this->assertNull($service->last_used_at);
    }

    public function test_revoke_token_clears_tokens_even_when_provider_fails()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_access_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->addHour(),
            'last_used_at' => now(),
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(false);

        $service->setProvider($mock);

        $result = $service->revokeToken();

        $this->assertFalse($result);

        // Verificar que los tokens fueron limpiados a pesar del fallo
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
        $this->assertNull($service->expires_at);
        $this->assertNull($service->last_used_at);
    }

    public function test_revoke_token_clears_tokens_when_provider_throws_exception()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_access_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->addHour(),
            'last_used_at' => now(),
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andThrow(new \Exception('Provider error'));

        $service->setProvider($mock);

        $result = $service->revokeToken();

        $this->assertFalse($result);

        // Verificar que los tokens fueron limpiados a pesar de la excepción
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
        $this->assertNull($service->expires_at);
        $this->assertNull($service->last_used_at);
    }

    public function test_revoke_token_preserves_other_service_data()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Test Service',
            'service_type' => 'google_drive',
            'slug' => 'test-service',
            'credentials' => ['client_id' => 'test_id'],
            'is_active' => true,
            'access_token' => 'valid_access_token',
            'refresh_token' => 'valid_refresh_token',
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service->setProvider($mock);

        $service->revokeToken();

        // Verificar que otros datos se preservan
        $service->refresh();
        $this->assertEquals('Test Service', $service->name);
        $this->assertEquals('google_drive', $service->service_type);
        $this->assertEquals('test-service', $service->slug);
        $this->assertEquals(['client_id' => 'test_id'], $service->credentials);
        $this->assertTrue($service->is_active);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
