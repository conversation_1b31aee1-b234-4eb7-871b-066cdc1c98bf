<?php

namespace LBCDev\OAuthManager\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Support\Str;

class OAuthServiceFactory extends Factory
{
    protected $model = OAuthService::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' ' . $this->faker->word,
            'slug' => Str::slug($this->faker->unique()->company),
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'fake_client_id',
                'client_secret' => 'fake_client_secret',
            ],
            'access_token' => 'fake_access_token',
            'refresh_token' => 'fake_refresh_token',
            'expires_at' => now()->addHour(),
            'is_active' => true,
            'last_used_at' => now(),
        ];
    }
}
