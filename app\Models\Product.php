<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;
use Aliziodev\LaravelTaxonomy\Enums\TaxonomyType;

class Product extends Model
{
    use HasFactory, HasTaxonomy;

    protected $fillable = [
        'nombre',
        'slug',
        'descripcion_corta',
        'descripcion_larga',
        'precio',
        'precio_descuento',
        'activo',
        'orden',
    ];

    public function files()
    {
        return $this->hasMany(File::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class);
    }

    public function languages()
    {
        return $this->belongsToMany(Language::class, 'pivot_products_language');
    }

    /**
     * Get all categories associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphToMany
     */
    public function categories()
    {
        return $this->taxonomies()->where('type', TaxonomyType::Category->value);
    }
}
