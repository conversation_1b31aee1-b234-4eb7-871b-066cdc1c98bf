<div class="space-y-4">
    {{-- OAuth Service Selection --}}
    @if(!$oauthServiceId)
        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-sm text-yellow-800">
                Please select a service first to browse files.
            </p>
        </div>
    @endif

    {{-- Error Display --}}
    @if($error)
        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-800">{{ $error }}</p>
        </div>
    @endif

    {{-- Service Type Display --}}
    @if($oauthServiceId && $serviceType)
        <div class="flex items-center text-sm text-gray-600 mb-2">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
            </svg>
            Browsing {{ $this->getServiceTypeDisplayName() }}
        </div>
    @endif

    {{-- Search Bar --}}
    @if($oauthServiceId)
        <div class="flex gap-2">
            <div class="flex-1">
                <input 
                    type="text" 
                    wire:model.defer="searchQuery"
                    wire:keydown.enter="search"
                    placeholder="Search files..."
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
            </div>
            <button 
                wire:click="search"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
                Search
            </button>
            @if($searchQuery)
                <button 
                    wire:click="clearSearch"
                    class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                    Clear
                </button>
            @endif
        </div>
    @endif

    {{-- Breadcrumb --}}
    @if($oauthServiceId && count($breadcrumb) > 0)
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                @foreach($breadcrumb as $index => $crumb)
                    <li class="flex items-center">
                        @if($index > 0)
                            <svg class="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                        <button 
                            wire:click="openFolder('{{ $crumb['id'] }}')"
                            class="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
                        >
                            {{ $crumb['name'] }}
                        </button>
                    </li>
                @endforeach
            </ol>
        </nav>
    @endif

    {{-- Loading Indicator --}}
    @if($isLoading)
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Loading...</span>
        </div>
    @endif

    {{-- Files List --}}
    @if($oauthServiceId && !$isLoading)
        <div class="border border-gray-200 rounded-lg overflow-hidden">
            @if(count($files) > 0)
                <div class="divide-y divide-gray-200">
                    @foreach($files as $file)
                        <div 
                            @if($file['isFolder'])
                                wire:click="openFolder('{{ $file['id'] }}')"
                            @else
                                wire:click="selectFile('{{ $file['id'] }}', '{{ addslashes($file['name']) }}', '{{ $file['webViewLink'] ?? $file['webContentLink'] ?? '' }}', '{{ $file['mimeType'] }}')"
                            @endif
                            class="flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-150 {{ $selectedFile && $selectedFile['id'] === $file['id'] ? 'bg-blue-50 border-l-4 border-blue-500' : '' }}"
                        >
                            {{-- File Icon --}}
                            <div class="flex-shrink-0 mr-3">
                                @if($file['iconLink'] ?? false)
                                    <img src="{{ $file['iconLink'] }}" alt="File icon" class="w-6 h-6">
                                @else
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($file['isFolder'])
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        @endif
                                    </svg>
                                @endif
                            </div>

                            {{-- File Info --}}
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ $file['name'] }}
                                        @if($file['isFolder'])
                                            <span class="text-xs text-gray-500 ml-1">(folder)</span>
                                        @endif
                                    </p>
                                    @if(!$file['isFolder'] && isset($file['sizeFormatted']))
                                        <p class="text-xs text-gray-500 ml-2">
                                            {{ $file['sizeFormatted'] }}
                                        </p>
                                    @endif
                                </div>
                                @if($file['modifiedTime'] ?? false)
                                    <p class="text-xs text-gray-500">
                                        Modified: {{ \Carbon\Carbon::parse($file['modifiedTime'])->format('M j, Y g:i A') }}
                                    </p>
                                @endif
                                @if(!empty($acceptedMimeTypes) && !in_array($file['mimeType'], $acceptedMimeTypes) && !$file['isFolder'])
                                    <p class="text-xs text-red-500">
                                        File type not accepted
                                    </p>
                                @endif
                            </div>

                            {{-- Selection Indicator --}}
                            @if($selectedFile && $selectedFile['id'] === $file['id'])
                                <div class="flex-shrink-0 ml-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <div class="p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No files found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        @if($searchQuery)
                            No files match your search criteria.
                        @else
                            This folder is empty.
                        @endif
                    </p>
                </div>
            @endif
        </div>
    @endif

    {{-- Selected File Info --}}
    @if($selectedFile)
        <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-green-800">Selected: {{ $selectedFile['name'] }}</p>
                    <p class="text-xs text-green-600">{{ $selectedFileUrl }}</p>
                </div>
            </div>
        </div>
    @endif
</div>
