<?php

namespace LBC<PERSON>ev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>BC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class RefreshTokenCommand extends Command
{
    protected $signature = 'oauth:refresh {service_id?} {--all}';
    protected $description = 'Refresh OAuth tokens';

    public function handle(OAuthManager $oauthManager): void
    {
        if ($this->option('all')) {
            $this->refreshAllTokens($oauthManager);
        } else {
            $this->refreshSingleToken($oauthManager);
        }
    }

    private function refreshAllTokens(OAuthManager $oauthManager): void
    {
        $services = OAuthService::where('is_active', true)
            ->whereNotNull('refresh_token')
            ->get();

        if ($services->isEmpty()) {
            $this->info('No services with refresh tokens found');
            return;
        }

        $this->info("Refreshing tokens for {$services->count()} services...");

        foreach ($services as $service) {
            if ($service->needsRefresh()) {
                $this->info("Refreshing token for: {$service->name}");

                if ($oauthManager->refreshTokenIfNeeded($service)) {
                    $this->info("✅ Token refreshed for {$service->name}");
                } else {
                    $this->error("❌ Failed to refresh token for {$service->name}");
                }
            } else {
                $this->info("Token for {$service->name} is still valid");
            }
        }
    }

    private function refreshSingleToken(OAuthManager $oauthManager): void
    {
        $serviceId = $this->argument('service_id');

        if (!$serviceId) {
            $this->error('Please provide a service ID or use --all flag');
            return;
        }

        $service = OAuthService::find($serviceId);

        if (!$service) {
            $this->error("Service with ID {$serviceId} not found");
            return;
        }

        if (!$service->refresh_token) {
            $this->error('Service does not have a refresh token');
            return;
        }

        $this->info("Refreshing token for: {$service->name}");

        if ($oauthManager->refreshTokenIfNeeded($service)) {
            $this->info('✅ Token refreshed successfully!');
        } else {
            $this->error('❌ Failed to refresh token');
        }
    }
}
