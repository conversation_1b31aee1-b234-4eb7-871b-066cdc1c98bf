<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use LBCDev\OAuthManager\Models\OAuthService;

class OneDriveServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear un servicio de ejemplo para OneDrive
        OAuthService::create([
            'name' => 'OneDrive Principal',
            'service_type' => 'onedrive',
            'slug' => 'onedrive-principal',
            'credentials' => [
                'client_id' => env('ONEDRIVE_CLIENT_ID'),
                'client_secret' => env('ONEDRIVE_CLIENT_SECRET'),
            ],
            'is_active' => true,
            'description' => 'Servicio principal de OneDrive para almacenamiento de archivos',
        ]);

        // Crear un segundo servicio de ejemplo (para demostrar múltiples cuentas)
        // OAuthService::create([
        //     'name' => 'OneDrive Backup',
        //     'service_type' => 'onedrive',
        //     'slug' => 'onedrive-backup',
        //     'credentials' => [
        //         'client_id' => env('ONEDRIVE_CLIENT_ID'),
        //         'client_secret' => env('ONEDRIVE_CLIENT_SECRET'),
        //     ],
        //     'is_active' => false, // Inactivo por defecto
        //     'description' => 'Servicio secundario de OneDrive para respaldos',
        // ]);
    }
}
