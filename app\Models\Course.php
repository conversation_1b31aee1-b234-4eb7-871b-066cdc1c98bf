<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Course extends Model
{
    /** @use HasFactory<\Database\Factories\CourseFactory> */
    use HasFactory;

    protected $fillable = [
        'titulo',
        'descripcion',
        'slug',
        'publicado',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }
}
