<?php

namespace LBCDev\OAuthManager\Tests\Feature\Commands;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class RevokeCommandTest extends TestCase
{
    use RefreshDatabase;

    public function test_revoke_single_service_command()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
            'refresh_token' => 'valid_refresh',
        ]);

        $this->artisan('oauth:revoke', ['service_id' => $service->id])
            ->expectsOutput("Revoking token for: {$service->name} ({$service->service_type})")
            ->assertExitCode(0);

        // Verificar que los tokens fueron limpiados (independientemente del resultado remoto)
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
    }

    public function test_revoke_single_service_command_with_invalid_id()
    {
        $this->artisan('oauth:revoke', ['service_id' => 999])
            ->expectsOutput('Service with ID 999 not found.')
            ->assertExitCode(0);
    }

    public function test_revoke_single_service_command_with_no_token()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => null,
        ]);

        $this->artisan('oauth:revoke', ['service_id' => $service->id])
            ->expectsOutput("Service '{$service->name}' has no active token to revoke.")
            ->assertExitCode(0);
    }

    public function test_revoke_all_tokens_command()
    {
        $service1 = OAuthService::factory()->create([
            'name' => 'Google Drive 1',
            'service_type' => 'google_drive',
            'access_token' => 'token1',
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'OneDrive 1',
            'service_type' => 'onedrive',
            'access_token' => 'token2',
        ]);

        // Service without token should be ignored
        OAuthService::factory()->create([
            'name' => 'Google Drive 2',
            'service_type' => 'google_drive',
            'access_token' => null,
        ]);

        $this->artisan('oauth:revoke', ['--all' => true])
            ->expectsOutput('Found 2 services with active tokens.')
            ->expectsOutput("Revoking token for: {$service1->name} ({$service1->service_type})")
            ->expectsOutput("Revoking token for: {$service2->name} ({$service2->service_type})")
            ->assertExitCode(0);

        // Verificar que los tokens fueron limpiados
        $service1->refresh();
        $service2->refresh();
        $this->assertNull($service1->access_token);
        $this->assertNull($service2->access_token);
    }

    public function test_revoke_all_tokens_command_with_no_services()
    {
        $this->artisan('oauth:revoke', ['--all' => true])
            ->expectsOutput('No services with active tokens found.')
            ->assertExitCode(0);
    }

    public function test_revoke_tokens_by_type_command()
    {
        $service1 = OAuthService::factory()->create([
            'name' => 'Google Drive 1',
            'service_type' => 'google_drive',
            'access_token' => 'token1',
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'Google Drive 2',
            'service_type' => 'google_drive',
            'access_token' => 'token2',
        ]);

        // OneDrive service should be ignored
        $onedriveService = OAuthService::factory()->create([
            'name' => 'OneDrive 1',
            'service_type' => 'onedrive',
            'access_token' => 'token3',
        ]);

        $this->artisan('oauth:revoke', ['--type' => 'google_drive'])
            ->expectsOutput("Found 2 services of type 'google_drive' with active tokens.")
            ->expectsOutput("Revoking token for: {$service1->name}")
            ->expectsOutput("Revoking token for: {$service2->name}")
            ->assertExitCode(0);

        // Verificar que solo los servicios de Google Drive fueron afectados
        $service1->refresh();
        $service2->refresh();
        $onedriveService->refresh();

        $this->assertNull($service1->access_token);
        $this->assertNull($service2->access_token);
        $this->assertNotNull($onedriveService->access_token); // No afectado
    }

    public function test_revoke_tokens_by_type_command_with_no_services()
    {
        $this->artisan('oauth:revoke', ['--type' => 'nonexistent_type'])
            ->expectsOutput("No services of type 'nonexistent_type' with active tokens found.")
            ->assertExitCode(0);
    }

    public function test_revoke_command_without_arguments()
    {
        $this->artisan('oauth:revoke')
            ->expectsOutput('Please provide a service ID or use --all or --type options.')
            ->assertExitCode(0);
    }

    public function test_revoke_command_handles_provider_failure_gracefully()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
        ]);

        $this->artisan('oauth:revoke', ['service_id' => $service->id])
            ->expectsOutput("Revoking token for: {$service->name} ({$service->service_type})")
            ->assertExitCode(0);

        // Token should be cleared locally regardless of remote result
        $service->refresh();
        $this->assertNull($service->access_token);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
