<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use LBCDev\OAuthManager\Providers\BaseOAuthProvider;
use Carbon\Carbon;
use Mockery;

class TokenRefreshTest extends TestCase
{
    use RefreshDatabase;

    public function test_refresh_token_if_needed_updates_tokens_correctly()
    {
        // Crear un servicio con token expirado
        $service = OAuthService::create([
            'name' => 'Test Service',
            'service_type' => 'google_drive',
            'slug' => 'test-service',
            'credentials' => ['client_id' => 'id', 'client_secret' => 'secret'],
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->subHour(),
            'is_active' => true,
        ]);

        // Mockear el provider para que devuelva nuevos tokens con expires_in
        $providerMock = Mockery::mock(\LBCDev\OAuthManager\Providers\BaseOAuthProvider::class);
        $providerMock->shouldReceive('refreshToken')
            ->once()
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_in' => 3600, // 1 hora
            ]);

        // Forzar que el servicio devuelva nuestro mock cuando se cree el provider
        /** @var \LBCDev\OAuthManager\Models\OAuthService $service */
        $service = Mockery::spy($service);
        $service->shouldReceive('getProviderInstance')
            ->andReturn($providerMock);

        $manager = new OAuthManager();

        $result = $manager->refreshTokenIfNeeded($service);

        $this->assertTrue($result);

        // Recargar el servicio para obtener los valores actualizados
        $updatedService = OAuthService::find($service->id);

        $this->assertEquals('new_access_token', $updatedService->access_token);
        $this->assertEquals('new_refresh_token', $updatedService->refresh_token);

        $this->assertNotNull($updatedService->expires_at);
        $this->assertTrue($updatedService->expires_at->gt(now()));
    }



    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
