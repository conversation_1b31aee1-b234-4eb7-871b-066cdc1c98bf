<?php

namespace LBCDev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>BC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class RevokeTokenCommand extends Command
{
    protected $signature = 'oauth:revoke {service_id?} {--all} {--type=}';
    protected $description = 'Revoke OAuth tokens';

    public function handle(OAuthManager $oauthManager): void
    {
        if ($this->option('all')) {
            $this->revokeAllTokens($oauthManager);
        } elseif ($this->option('type')) {
            $this->revokeTokensByType($oauthManager);
        } else {
            $this->revokeSingleToken($oauthManager);
        }
    }

    private function revokeAllTokens(OAuthManager $oauthManager): void
    {
        $services = OAuthService::whereNotNull('access_token')->get();

        if ($services->isEmpty()) {
            $this->info('No services with active tokens found.');
            return;
        }

        $this->info("Found {$services->count()} services with active tokens.");

        $revokedCount = 0;
        $failedCount = 0;

        foreach ($services as $service) {
            $this->line("Revoking token for: {$service->name} ({$service->service_type})");

            if ($service->revokeToken()) {
                $this->info("✓ Successfully revoked token for {$service->name}");
                $revokedCount++;
            } else {
                $this->error("✗ Failed to revoke token for {$service->name}");
                $failedCount++;
            }
        }

        $this->info("Revocation complete: {$revokedCount} successful, {$failedCount} failed.");
    }

    private function revokeTokensByType(OAuthManager $oauthManager): void
    {
        $serviceType = $this->option('type');
        $services = OAuthService::where('service_type', $serviceType)
            ->whereNotNull('access_token')
            ->get();

        if ($services->isEmpty()) {
            $this->info("No services of type '{$serviceType}' with active tokens found.");
            return;
        }

        $this->info("Found {$services->count()} services of type '{$serviceType}' with active tokens.");

        $revokedCount = 0;
        $failedCount = 0;

        foreach ($services as $service) {
            $this->line("Revoking token for: {$service->name}");

            if ($service->revokeToken()) {
                $this->info("✓ Successfully revoked token for {$service->name}");
                $revokedCount++;
            } else {
                $this->error("✗ Failed to revoke token for {$service->name}");
                $failedCount++;
            }
        }

        $this->info("Revocation complete: {$revokedCount} successful, {$failedCount} failed.");
    }

    private function revokeSingleToken(OAuthManager $oauthManager): void
    {
        $serviceId = $this->argument('service_id');

        if (!$serviceId) {
            $this->error('Please provide a service ID or use --all or --type options.');
            return;
        }

        $service = OAuthService::find($serviceId);

        if (!$service) {
            $this->error("Service with ID {$serviceId} not found.");
            return;
        }

        if (!$service->access_token) {
            $this->info("Service '{$service->name}' has no active token to revoke.");
            return;
        }

        $this->line("Revoking token for: {$service->name} ({$service->service_type})");

        if ($service->revokeToken()) {
            $this->info("✓ Successfully revoked token for {$service->name}");
        } else {
            $this->error("✗ Failed to revoke token for {$service->name}");
        }
    }
}
