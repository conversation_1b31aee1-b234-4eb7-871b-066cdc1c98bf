<?php

/**
 * Ejemplo de uso del MegaProvider
 * 
 * Este archivo muestra cómo usar el MegaProvider para:
 * - Crear un servicio OAuth de Mega
 * - Autorizar el servicio
 * - Obtener tokens válidos
 * - Usar la API de Mega
 */

require_once __DIR__ . '/../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

// 1. Crear un servicio OAuth de Mega
$megaService = OAuthService::create([
    'name' => 'Mi cuenta de Mega',
    'service_type' => 'mega',
    'slug' => 'mi-mega',
    'credentials' => [
        'email' => '<EMAIL>',
        'password' => 'tu-password-mega',
    ],
    'is_active' => true,
]);

echo "Servicio Mega creado con ID: {$megaService->id}\n";

// 2. Obtener el provider
$provider = $megaService->getProviderInstance();

// 3. Simular el proceso de autorización
// En Mega, no hay OAuth tradicional, pero simulamos el flujo
try {
    // En una aplicación real, esto se haría a través del navegador
    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'tu-password-mega'
    ];
    
    // Codificar credenciales para simular el callback
    $encodedCredentials = base64_encode(json_encode($credentials));
    
    // Manejar el "callback" (en realidad, hacer login directo)
    $tokenData = $provider->handleCallback($encodedCredentials);
    
    // Actualizar el servicio con los tokens
    $megaService->update([
        'access_token' => $tokenData['access_token'],
        'refresh_token' => $tokenData['refresh_token'],
        'expires_at' => $tokenData['expires_at'],
        'is_authorized' => true,
    ]);
    
    echo "Autorización completada exitosamente.\n";
    
} catch (Exception $e) {
    echo "Error durante la autorización: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. Usar OAuthManager para obtener token válido
$oauthManager = new OAuthManager();

// Obtener token válido
$token = $oauthManager->getValidToken('mega');

if ($token) {
    echo "Token válido obtenido: " . substr($token, 0, 20) . "...\n";
    
    // 5. Usar el provider directamente
    try {
        // Test de conexión
        $connectionTest = $provider->testConnection();
        echo "Test de conexión: " . ($connectionTest ? 'ÉXITO' : 'FALLO') . "\n";
        
        // Obtener información del usuario
        $userInfo = $provider->getUserInfo();
        echo "Usuario: " . ($userInfo['email'] ?? 'N/A') . "\n";
        echo "Almacenamiento usado: " . formatBytes($userInfo['storage_used'] ?? 0) . "\n";
        echo "Número de archivos: " . ($userInfo['file_count'] ?? 0) . "\n";
        
        // Listar archivos
        $files = $provider->getFiles();
        echo "Archivos encontrados: " . count($files) . "\n";
        
        // Mostrar algunos archivos
        if (!empty($files)) {
            echo "\nPrimeros 5 archivos/carpetas:\n";
            foreach (array_slice($files, 0, 5) as $file) {
                $type = $file['type'] === 'file' ? '📄' : '📁';
                $size = $file['type'] === 'file' ? ' (' . formatBytes($file['size']) . ')' : '';
                echo "  {$type} {$file['name']}{$size}\n";
            }
        }
        
        // Ejemplo de filtrar archivos por carpeta padre
        if (!empty($files)) {
            $folders = array_filter($files, function($item) {
                return $item['type'] === 'folder';
            });
            
            if (!empty($folders)) {
                $firstFolder = reset($folders);
                echo "\nArchivos en la carpeta '{$firstFolder['name']}':\n";
                
                $folderFiles = $provider->getFiles($firstFolder['id']);
                foreach ($folderFiles as $file) {
                    $type = $file['type'] === 'file' ? '📄' : '📁';
                    $size = $file['type'] === 'file' ? ' (' . formatBytes($file['size']) . ')' : '';
                    echo "  {$type} {$file['name']}{$size}\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "Error al usar la API de Mega: " . $e->getMessage() . "\n";
    }
} else {
    echo "No se pudo obtener un token válido.\n";
}

// 6. Ejemplo de renovación de token
echo "\n--- Renovación de Token ---\n";
try {
    $newTokenData = $provider->refreshToken();
    if ($newTokenData) {
        $megaService->update([
            'access_token' => $newTokenData['access_token'],
            'refresh_token' => $newTokenData['refresh_token'],
            'expires_at' => $newTokenData['expires_at'],
        ]);
        echo "Token renovado exitosamente.\n";
    } else {
        echo "No se pudo renovar el token.\n";
    }
} catch (Exception $e) {
    echo "Error al renovar token: " . $e->getMessage() . "\n";
}

// 7. Ejemplo de revocación de token
echo "\n--- Revocación de Token ---\n";
try {
    $revoked = $provider->revokeToken();
    if ($revoked) {
        $megaService->update([
            'access_token' => null,
            'refresh_token' => null,
            'expires_at' => null,
            'is_authorized' => false,
        ]);
        echo "Token revocado exitosamente.\n";
    } else {
        echo "No se pudo revocar el token.\n";
    }
} catch (Exception $e) {
    echo "Error al revocar token: " . $e->getMessage() . "\n";
}

/**
 * Función auxiliar para formatear bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n--- Ejemplo completado ---\n";

/**
 * NOTAS IMPORTANTES:
 * 
 * 1. Credenciales de Mega:
 *    - A diferencia de otros servicios OAuth, Mega requiere email y password directamente
 *    - No hay un proceso de registro de aplicación como en Google Drive o Dropbox
 *    - Las credenciales se almacenan de forma segura en la base de datos
 * 
 * 2. Seguridad:
 *    - Asegúrate de usar HTTPS en producción
 *    - Las credenciales se almacenan encriptadas en la base de datos
 *    - Considera usar variables de entorno para credenciales sensibles
 * 
 * 3. Limitaciones:
 *    - Mega no tiene un sistema OAuth tradicional
 *    - Los tokens de sesión pueden expirar, requiriendo re-autenticación
 *    - La API de Mega es más limitada comparada con Google Drive o Dropbox
 * 
 * 4. Uso en producción:
 *    - Implementa manejo de errores robusto
 *    - Considera implementar retry logic para operaciones fallidas
 *    - Monitorea el uso de la API para evitar límites de rate
 */
