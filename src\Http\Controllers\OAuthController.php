<?php

namespace LBCDev\OAuthManager\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class OAuthController extends Controller
{
    public function authorize(Request $request, OAuthService $service)
    {
        // Guardar la URL de redirección en la sesión si se proporciona
        if ($request->has('redirect_url')) {
            session(['oauth_redirect_url' => $request->get('redirect_url')]);
        }

        $provider = $service->getProviderInstance();
        $authUrl = $provider->getAuthorizationUrl();

        return redirect($authUrl);
    }

    public function callback(Request $request, OAuthService $service)
    {
        if ($request->has('error')) {
            return $this->redirectAfterCallback($request, 'error', 'Authorization failed: ' . $request->get('error_description'));
        }

        $code = $request->get('code');
        if (!$code) {
            return $this->redirectAfterCallback($request, 'error', 'Authorization code not received');
        }

        try {
            $provider = $service->getProviderInstance();
            $tokenData = $provider->handleCallback($code);

            $service->update([
                'access_token' => $tokenData['access_token'],
                'refresh_token' => $tokenData['refresh_token'],
                'expires_at' => $tokenData['expires_at'],
            ]);

            return $this->redirectAfterCallback($request, 'success', 'Service authorized successfully!');
        } catch (\Exception $e) {
            return $this->redirectAfterCallback($request, 'error', 'Authorization failed: ' . $e->getMessage());
        }
    }

    /**
     * Determinar a dónde redirigir después del callback OAuth
     */
    protected function redirectAfterCallback(Request $request, string $messageType, string $message)
    {
        // 1. Prioridad: URL de redirección en la sesión (pasada durante authorize)
        $redirectUrl = session()->pull('oauth_redirect_url');

        // 2. Segunda prioridad: URL de redirección en la configuración
        if (!$redirectUrl) {
            $redirectUrl = config('oauth-manager.default_redirect_url');
        }

        // 3. Tercera prioridad: URL de redirección en el request
        if (!$redirectUrl && $request->has('redirect_url')) {
            $redirectUrl = $request->get('redirect_url');
        }

        // 4. Fallback: redirect()->back() (comportamiento original)
        if (!$redirectUrl) {
            return redirect()->back()->with($messageType, $message);
        }

        return redirect($redirectUrl)->with($messageType, $message);
    }
}
