<?php

namespace LBC<PERSON>ev\OAuthFileExplorer;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use LBCDev\OAuthFileExplorer\Livewire\OAuthFileExplorer;

class OAuthFileExplorerServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/oauth-file-explorer.php',
            'oauth-file-explorer'
        );
    }

    public function boot()
    {
        // Publish config
        $this->publishes([
            __DIR__.'/../config/oauth-file-explorer.php' => config_path('oauth-file-explorer.php'),
        ], 'oauth-file-explorer-config');

        // Publish views
        $this->publishes([
            __DIR__.'/../resources/views' => resource_path('views/vendor/oauth-file-explorer'),
        ], 'oauth-file-explorer-views');

        // Load views
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'oauth-file-explorer');

        // Register Livewire component
        Livewire::component('oauth-file-explorer', OAuthFileExplorer::class);
    }
}
