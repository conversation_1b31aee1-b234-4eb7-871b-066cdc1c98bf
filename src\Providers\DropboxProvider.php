<?php

namespace LBCDev\OAuthManager\Providers;

use <PERSON><PERSON>\Dropbox\Client as DropboxClient;
use GuzzleHttp\Client;
use Exception;

class DropboxProvider extends BaseOAuthProvider
{
    protected function getDropboxClient(): DropboxClient
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $accessToken = $this->service->access_token;

        if (!$accessToken) {
            throw new Exception('No access token available');
        }

        return new DropboxClient($accessToken);
    }

    public function getAuthorizationUrl(): string
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;

        if (!$clientId) {
            throw new Exception('Missing client_id in credentials');
        }

        $scopes = implode(' ', $this->config['scopes'] ?? []);
        $redirectUri = $this->getRedirectUri();
        $state = bin2hex(random_bytes(16));

        session(['oauth2state' => $state]);

        $params = http_build_query([
            'client_id' => $clientId,
            'response_type' => 'code',
            'redirect_uri' => $redirectUri,
            'scope' => $scopes,
            'state' => $state,
            'token_access_type' => 'offline', // Para obtener refresh token
        ]);

        return 'https://www.dropbox.com/oauth2/authorize?' . $params;
    }

    public function handleCallback(string $code): array
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            throw new Exception('Missing client credentials');
        }

        try {
            $client = new Client();
            
            $response = $client->post('https://api.dropboxapi.com/oauth2/token', [
                'form_params' => [
                    'code' => $code,
                    'grant_type' => 'authorization_code',
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                    'redirect_uri' => $this->getRedirectUri(),
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'access_token' => $data['access_token'],
                'refresh_token' => $data['refresh_token'] ?? null,
                'expires_at' => isset($data['expires_in']) ? now()->addSeconds($data['expires_in']) : null,
            ];
        } catch (Exception $e) {
            throw new Exception('Failed to exchange code for token: ' . $e->getMessage());
        }
    }

    public function refreshToken(): ?array
    {
        if (!$this->service->refresh_token) {
            return null;
        }

        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            return null;
        }

        try {
            $client = new Client();
            
            $response = $client->post('https://api.dropboxapi.com/oauth2/token', [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $this->service->refresh_token,
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'access_token' => $data['access_token'],
                'refresh_token' => $data['refresh_token'] ?? $this->service->refresh_token,
                'expires_at' => isset($data['expires_in']) ? now()->addSeconds($data['expires_in']) : null,
            ];
        } catch (Exception $e) {
            return null;
        }
    }

    public function revokeToken(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = new Client();
            
            $response = $client->post('https://api.dropboxapi.com/2/auth/token/revoke', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (Exception $e) {
            return false;
        }
    }

    public function testConnection(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = new Client();
            
            // Test connection by getting current account info
            $response = $client->post('https://api.dropboxapi.com/2/users/get_current_account', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get Dropbox files and folders
     */
    public function getFiles(string $path = ''): array
    {
        try {
            $dropboxClient = $this->getDropboxClient();
            
            $response = $dropboxClient->listFolder($path);
            
            return $response['entries'] ?? [];
        } catch (Exception $e) {
            throw new Exception('Failed to get Dropbox files: ' . $e->getMessage());
        }
    }

    /**
     * Get Dropbox user info
     */
    public function getUserInfo(): array
    {
        if (!$this->service->access_token) {
            throw new Exception('No access token available');
        }

        try {
            $client = new Client();
            
            $response = $client->post('https://api.dropboxapi.com/2/users/get_current_account', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (Exception $e) {
            throw new Exception('Failed to get user info: ' . $e->getMessage());
        }
    }
}
