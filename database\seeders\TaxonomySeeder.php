<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Aliziodev\LaravelTaxonomy\Facades\Taxonomy;

class TaxonomySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Taxonomy::create([
            'name' => 'Audios',
            'type' => 'category',
            'description' => 'Audios',
        ]);

        Taxonomy::create([
            'name' => 'Cursos',
            'type' => 'category',
            'description' => 'Cursos',
        ]);

        Taxonomy::create([
            'name' => 'Coaching',
            'type' => 'category',
            'description' => 'Coaching',
        ]);

        Taxonomy::create([
            'name' => 'Varios',
            'type' => 'category',
            'description' => 'Varios',
        ]);
    }
}
