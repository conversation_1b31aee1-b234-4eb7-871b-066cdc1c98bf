<?php

namespace LBCDev\OAuthManager\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>BC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use LBCDev\OAuthManager\Tests\TestCase;
use Mockery;

class OAuthManagerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->app['config']->set('oauth-manager.services.google_drive', [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'slug' => 'google-drive',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => ['https://www.googleapis.com/auth/drive'],
            'fields' => [
                'client_id' => 'Client ID',
                'client_secret' => 'Client Secret',
            ]
        ]);
    }

    public function test_get_service_returns_correct_instance()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'name' => 'Drive 1',
            'is_active' => true,
        ]);

        $manager = new OAuthManager();

        $found = $manager->getService('google_drive', 'Drive 1');

        $this->assertNotNull($found);
        $this->assertEquals($service->id, $found->id);
    }

    public function test_refresh_token_if_needed_skips_if_not_expired()
    {
        $service = OAuthService::factory()->create([
            'access_token' => 'valid_token',
            'refresh_token' => 'refresh_token',
            'expires_at' => now()->addHour(),
        ]);

        $manager = new OAuthManager();

        $this->assertTrue($manager->refreshTokenIfNeeded($service));
    }

    public function test_refresh_token_if_needed_refreshes_and_updates_token()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'old_token',
            'refresh_token' => 'refresh_token',
            'expires_at' => now()->subMinute(),
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('refreshToken')->once()->andReturn([
            'access_token' => 'new_token',
            'refresh_token' => 'new_refresh_token',
            'expires_in' => 3600,
        ]);

        $service->setProvider($mock);
        $service->save();

        $manager = new OAuthManager();
        $manager->refreshTokenIfNeeded($service);

        $service->refresh();

        $this->assertEquals('new_token', $service->access_token);
        $this->assertEquals('new_refresh_token', $service->refresh_token);
        $this->assertNotNull($service->expires_at);
    }

    public function test_get_valid_token_refreshes_and_returns_token()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Drive 1',
            'service_type' => 'google_drive',
            'access_token' => 'expired_token',
            'refresh_token' => 'refresh_token',
            'expires_at' => now()->subMinute(),
            'is_active' => true,
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('refreshToken')->once()->andReturn([
            'access_token' => 'refreshed_token',
            'refresh_token' => 'refresh_token',
            'expires_in' => 3600,
        ]);

        $service->setProvider($mock);

        $manager = new OAuthManager();

        // Testear directamente el refresh
        $this->assertTrue($manager->refreshTokenIfNeeded($service));

        // Verificar que el token se actualizó
        $service->refresh(); // Recargar desde la DB
        $this->assertEquals('refreshed_token', $service->access_token);
    }
}
