<?php

namespace Tests\Feature;

use App\Livewire\CartIcon;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class CartNavigationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test product
        Product::create([
            'nombre' => 'Test Product',
            'descripcion_corta' => 'Test description',
            'precio' => 99.99,
            'activo' => true,
            'slug' => 'test-product',
        ]);
    }

    public function test_cart_icon_component_renders(): void
    {
        Livewire::test(CartIcon::class)
            ->assertStatus(200)
            ->assertSet('count', 0)
            ->assertSet('total', 0);
    }

    public function test_cart_icon_updates_when_cart_changes(): void
    {
        // Add item to session cart
        session(['cart' => [
            1 => [
                'id' => 1,
                'titulo' => 'Test Product',
                'precio' => 99.99,
                'quantity' => 2,
                'slug' => 'test-product',
            ]
        ]]);

        Livewire::test(CartIcon::class)
            ->call('loadCart')
            ->assertSet('count', 2)
            ->assertSet('total', 199.98);
    }

    public function test_cart_icon_responds_to_cart_updated_event(): void
    {
        // Start with empty cart
        Livewire::test(CartIcon::class)
            ->assertSet('count', 0);

        // Add item to session
        session(['cart' => [
            1 => [
                'id' => 1,
                'nombre' => 'Test Product',
                'precio' => 99.99,
                'quantity' => 1,
                'slug' => 'test-product',
            ]
        ]]);

        // Trigger the event that should update the cart
        Livewire::test(CartIcon::class)
            ->dispatch('cartUpdated')
            ->assertSet('count', 1)
            ->assertSet('total', 99.99);
    }

    public function test_cart_api_endpoints_work(): void
    {
        // Test cart API endpoint
        $response = $this->getJson('/cart/');
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'cart' => [],
                'total' => 0,
                'count' => 0,
            ]);
    }

    public function test_cart_icon_contains_cart_link(): void
    {
        Livewire::test(CartIcon::class)
            ->assertSee('/cart/view'); // Check that the link is present
    }

    public function test_cart_icon_shows_correct_badge_for_multiple_items(): void
    {
        // Add multiple different products
        session(['cart' => [
            1 => [
                'id' => 1,
                'nombre' => 'Product 1',
                'precio' => 50.00,
                'quantity' => 2,
                'slug' => 'product-1',
            ],
            2 => [
                'id' => 2,
                'nombre' => 'Product 2',
                'precio' => 25.00,
                'quantity' => 3,
                'slug' => 'product-2',
            ]
        ]]);

        Livewire::test(CartIcon::class)
            ->call('loadCart')
            ->assertSet('count', 5) // 2 + 3
            ->assertSet('total', 175.00); // (50*2) + (25*3)
    }

    public function test_cart_icon_handles_large_quantities(): void
    {
        // Test with more than 99 items
        session(['cart' => [
            1 => [
                'id' => 1,
                'titulo' => 'Product 1',
                'precio' => 1.00,
                'quantity' => 150,
                'slug' => 'product-1',
            ]
        ]]);

        Livewire::test(CartIcon::class)
            ->call('loadCart')
            ->assertSet('count', 150)
            ->assertSee('99+'); // Should show 99+ for quantities over 99
    }
}
