<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Aliziodev\LaravelTaxonomy\Facades\Taxonomy;
use Aliziodev\LaravelTaxonomy\Enums\TaxonomyType;

class ProductTaxonomySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear categorías principales de productos
        $electronics = Taxonomy::create([
            'name' => 'Electrónicos',
            'slug' => 'electronicos',
            'type' => TaxonomyType::Category->value,
            'description' => 'Productos electrónicos y tecnológicos',
            'meta' => [
                'icon' => 'laptop',
                'color' => '#007bff',
                'featured' => true,
            ],
        ]);

        $clothing = Taxonomy::create([
            'name' => 'Ropa y Accesorios',
            'slug' => 'ropa-accesorios',
            'type' => TaxonomyType::Category->value,
            'description' => 'Ropa, calzado y accesorios de moda',
            'meta' => [
                'icon' => 'shirt',
                'color' => '#28a745',
                'featured' => true,
            ],
        ]);

        $home = Taxonomy::create([
            'name' => 'Hogar y Jardín',
            'slug' => 'hogar-jardin',
            'type' => TaxonomyType::Category->value,
            'description' => 'Productos para el hogar y jardín',
            'meta' => [
                'icon' => 'home',
                'color' => '#ffc107',
                'featured' => true,
            ],
        ]);

        $sports = Taxonomy::create([
            'name' => 'Deportes y Fitness',
            'slug' => 'deportes-fitness',
            'type' => TaxonomyType::Category->value,
            'description' => 'Equipos deportivos y fitness',
            'meta' => [
                'icon' => 'dumbbell',
                'color' => '#dc3545',
                'featured' => true,
            ],
        ]);

        // Subcategorías de Electrónicos
        Taxonomy::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $electronics->id,
            'description' => 'Teléfonos inteligentes y accesorios',
        ]);

        Taxonomy::create([
            'name' => 'Laptops',
            'slug' => 'laptops',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $electronics->id,
            'description' => 'Computadoras portátiles',
        ]);

        Taxonomy::create([
            'name' => 'Tablets',
            'slug' => 'tablets',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $electronics->id,
            'description' => 'Tabletas y accesorios',
        ]);

        Taxonomy::create([
            'name' => 'Audio y Video',
            'slug' => 'audio-video',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $electronics->id,
            'description' => 'Equipos de audio y video',
        ]);

        // Subcategorías de Ropa
        Taxonomy::create([
            'name' => 'Ropa Masculina',
            'slug' => 'ropa-masculina',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $clothing->id,
            'description' => 'Ropa para hombres',
        ]);

        Taxonomy::create([
            'name' => 'Ropa Femenina',
            'slug' => 'ropa-femenina',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $clothing->id,
            'description' => 'Ropa para mujeres',
        ]);

        Taxonomy::create([
            'name' => 'Calzado',
            'slug' => 'calzado',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $clothing->id,
            'description' => 'Zapatos y calzado deportivo',
        ]);

        // Subcategorías de Hogar
        Taxonomy::create([
            'name' => 'Muebles',
            'slug' => 'muebles',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $home->id,
            'description' => 'Muebles para el hogar',
        ]);

        Taxonomy::create([
            'name' => 'Decoración',
            'slug' => 'decoracion',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $home->id,
            'description' => 'Artículos decorativos',
        ]);

        Taxonomy::create([
            'name' => 'Electrodomésticos',
            'slug' => 'electrodomesticos',
            'type' => TaxonomyType::Category->value,
            'parent_id' => $home->id,
            'description' => 'Electrodomésticos para el hogar',
        ]);

        // Tags para productos
        Taxonomy::create([
            'name' => 'Destacado',
            'slug' => 'destacado',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Productos destacados',
            'meta' => [
                'color' => '#ff6b6b',
                'priority' => 1,
            ],
        ]);

        Taxonomy::create([
            'name' => 'Oferta',
            'slug' => 'oferta',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Productos en oferta',
            'meta' => [
                'color' => '#4ecdc4',
                'priority' => 2,
            ],
        ]);

        Taxonomy::create([
            'name' => 'Nuevo',
            'slug' => 'nuevo',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Productos nuevos',
            'meta' => [
                'color' => '#45b7d1',
                'priority' => 3,
            ],
        ]);

        Taxonomy::create([
            'name' => 'Bestseller',
            'slug' => 'bestseller',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Productos más vendidos',
            'meta' => [
                'color' => '#f9ca24',
                'priority' => 4,
            ],
        ]);

        Taxonomy::create([
            'name' => 'Eco-friendly',
            'slug' => 'eco-friendly',
            'type' => TaxonomyType::Tag->value,
            'description' => 'Productos ecológicos',
            'meta' => [
                'color' => '#6c5ce7',
                'priority' => 5,
            ],
        ]);

        // Marcas como taxonomías
        Taxonomy::create([
            'name' => 'Apple',
            'slug' => 'apple',
            'type' => 'brand',
            'description' => 'Productos Apple',
            'meta' => [
                'logo' => 'apple-logo.png',
                'website' => 'https://apple.com',
            ],
        ]);

        Taxonomy::create([
            'name' => 'Samsung',
            'slug' => 'samsung',
            'type' => 'brand',
            'description' => 'Productos Samsung',
            'meta' => [
                'logo' => 'samsung-logo.png',
                'website' => 'https://samsung.com',
            ],
        ]);

        Taxonomy::create([
            'name' => 'Nike',
            'slug' => 'nike',
            'type' => 'brand',
            'description' => 'Productos Nike',
            'meta' => [
                'logo' => 'nike-logo.png',
                'website' => 'https://nike.com',
            ],
        ]);

        Taxonomy::create([
            'name' => 'Adidas',
            'slug' => 'adidas',
            'type' => 'brand',
            'description' => 'Productos Adidas',
            'meta' => [
                'logo' => 'adidas-logo.png',
                'website' => 'https://adidas.com',
            ],
        ]);

        // Colores como taxonomías
        // $colors = [
        //     ['name' => 'Negro', 'slug' => 'negro', 'hex' => '#000000'],
        //     ['name' => 'Blanco', 'slug' => 'blanco', 'hex' => '#ffffff'],
        //     ['name' => 'Rojo', 'slug' => 'rojo', 'hex' => '#ff0000'],
        //     ['name' => 'Azul', 'slug' => 'azul', 'hex' => '#0000ff'],
        //     ['name' => 'Verde', 'slug' => 'verde', 'hex' => '#00ff00'],
        //     ['name' => 'Amarillo', 'slug' => 'amarillo', 'hex' => '#ffff00'],
        //     ['name' => 'Rosa', 'slug' => 'rosa', 'hex' => '#ffc0cb'],
        //     ['name' => 'Gris', 'slug' => 'gris', 'hex' => '#808080'],
        // ];

        // foreach ($colors as $color) {
        //     Taxonomy::create([
        //         'name' => $color['name'],
        //         'slug' => $color['slug'],
        //         'type' => TaxonomyType::Color->value,
        //         'description' => "Color {$color['name']}",
        //         'meta' => [
        //             'hex' => $color['hex'],
        //         ],
        //     ]);
        // }

        // Tallas como taxonomías
        // $sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
        // foreach ($sizes as $size) {
        //     Taxonomy::create([
        //         'name' => $size,
        //         'slug' => strtolower($size),
        //         'type' => TaxonomyType::Size->value,
        //         'description' => "Talla {$size}",
        //     ]);
        // }
    }
}
