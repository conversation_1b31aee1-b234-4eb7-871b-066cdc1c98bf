<?php

namespace LBCDev\OAuthFileExplorer\Tests\Feature;

use LBCDev\OAuthFileExplorer\Tests\TestCase;
use LBCDev\OAuthFileExplorer\Livewire\OAuthFileExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;

class BasicComponentTest extends TestCase
{
    #[Test]
    public function it_can_instantiate_component()
    {
        $component = new OAuthFileExplorer();
        $this->assertInstanceOf(OAuthFileExplorer::class, $component);
    }

    #[Test]
    public function it_has_correct_initial_state()
    {
        $component = new OAuthFileExplorer();
        $this->assertNull($component->oauthServiceId);
        $this->assertSame('root', $component->currentFolderId);
        $this->assertSame([], $component->files);
        $this->assertSame([], $component->breadcrumb);
        $this->assertNull($component->selectedFile);
        $this->assertSame('', $component->selectedFileUrl);
        $this->assertSame('', $component->searchQuery);
        $this->assertFalse($component->isLoading);
        $this->assertNull($component->error);
        $this->assertNull($component->serviceType);
        $this->assertSame([], $component->acceptedMimeTypes);
    }

    #[Test]
    public function it_returns_service_display_names()
    {
        $component = new OAuthFileExplorer();

        $component->serviceType = 'google_drive';
        $result = $component->getServiceTypeDisplayName();
        $this->assertSame('Google Drive', $result);

        $component->serviceType = 'dropbox';
        $result = $component->getServiceTypeDisplayName();
        $this->assertSame('Dropbox', $result);

        $component->serviceType = 'unknown_service';
        $result = $component->getServiceTypeDisplayName();
        $this->assertSame('Unknown service', $result);
    }

    #[Test]
    public function it_can_mount_with_parameters()
    {
        $component = new OAuthFileExplorer();
        $component->mount(123, 'custom_field', ['application/pdf']);

        $this->assertSame(123, $component->oauthServiceId);
        $this->assertSame('custom_field', $component->fieldName);
        $this->assertSame(['application/pdf'], $component->acceptedMimeTypes);
    }

    #[Test]
    public function it_can_open_folder()
    {
        $component = new OAuthFileExplorer();
        $component->openFolder('folder123');

        $this->assertSame('folder123', $component->currentFolderId);
        $this->assertSame('', $component->searchQuery);
    }

    #[Test]
    public function it_can_clear_search()
    {
        $component = new OAuthFileExplorer();
        $component->searchQuery = 'test query';
        $component->clearSearch();

        $this->assertSame('', $component->searchQuery);
    }

    #[Test]
    public function it_rejects_unaccepted_mime_types()
    {
        $component = new OAuthFileExplorer();
        $component->acceptedMimeTypes = ['application/pdf'];

        $component->selectFile('file123', 'test.jpg', 'http://example.com/test.jpg', 'image/jpeg');

        // Should have an error and no selected file
        $this->assertNotEmpty($component->error);
        $this->assertEmpty($component->selectedFile);
    }

    #[Test]
    public function it_accepts_valid_mime_types()
    {
        $component = new OAuthFileExplorer();
        $component->acceptedMimeTypes = ['image/jpeg'];

        $component->selectFile('file123', 'test.jpg', 'http://example.com/test.jpg', 'image/jpeg');

        // Should have no error and a selected file
        $this->assertEmpty($component->error);
        $this->assertNotEmpty($component->selectedFile);
        $this->assertSame('file123', $component->selectedFile['id']);
        $this->assertSame('test.jpg', $component->selectedFile['name']);
        $this->assertSame('http://example.com/test.jpg', $component->selectedFileUrl);
    }
}
