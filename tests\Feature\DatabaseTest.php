<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;

class DatabaseTest extends TestCase
{
    use RefreshDatabase;

    public function test_oauth_services_table_has_correct_structure()
    {
        $this->assertTrue(Schema::hasTable('oauth_services'));

        $columns = Schema::getColumnListing('oauth_services');

        $expectedColumns = [
            'id',
            'name',
            'service_type',
            'slug',
            'credentials',
            'access_token',
            'refresh_token',
            'expires_at',
            'is_active',
            'last_used_at',
            'created_at',
            'updated_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertContains($column, $columns);
        }
    }

    public function test_unique_constraint_works()
    {
        OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'name' => 'Test Service',
            'slug' => 'test-service',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'name' => 'Test Service',
            'slug' => 'test-service',
        ]);
    }

    public function test_factory_creates_valid_models()
    {
        $service = OAuthService::factory()->create();

        $this->assertInstanceOf(OAuthService::class, $service);
        $this->assertNotNull($service->name);
        $this->assertNotNull($service->service_type);
        $this->assertNotNull($service->slug);
        $this->assertIsArray($service->credentials);
        $this->assertTrue($service->is_active);
    }
}
