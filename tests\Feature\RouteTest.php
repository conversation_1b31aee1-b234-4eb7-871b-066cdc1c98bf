<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class RouteTest extends TestCase
{
    use RefreshDatabase;

    public function test_test_paquete_route_works()
    {
        $response = $this->get('/test-paquete');

        $response->assertStatus(200);
        $response->assertSeeText('Paquete funcionando');
    }

    public function test_authorize_route_exists()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        // Refresh the model to ensure it's properly loaded from database
        $service = $service->fresh();

        $this->assertIsArray($service->credentials);
        $this->assertArrayHasKey('client_id', $service->credentials);
        $this->assertArrayHasKey('client_secret', $service->credentials);
        $this->assertEquals('test_client_id', $service->credentials['client_id']);
        $this->assertEquals('test_client_secret', $service->credentials['client_secret']);

        // Debug: Let's see what we have in the database
        $dbService = OAuthService::where('slug', 'google-drive')->first();
        $this->assertNotNull($dbService);
        $this->assertIsArray($dbService->credentials);

        // Test that the provider can be instantiated correctly
        $provider = $service->getProviderInstance();
        $this->assertInstanceOf(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $provider);

        // Mock the provider to avoid the actual OAuth call
        $mockProvider = \Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(\Mockery::type(\LBCDev\OAuthManager\Models\OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('getAuthorizationUrl')
            ->andReturn('https://accounts.google.com/o/oauth2/auth?client_id=test');

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.authorize', $service->slug));

        // Debería redirigir a la URL de autorización
        $response->assertStatus(302);
    }

    public function test_callback_route_exists()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $response = $this->get(route('oauth-manager.callback', $service) . '?error=access_denied');

        $response->assertStatus(302);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
