<?php

namespace LBCDev\OAuthManager\Providers;

use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Token\AccessToken;

class YouTube<PERSON>rovider extends BaseOAuthProvider
{
    protected function getProvider(): Google
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            throw new \Exception('Missing client credentials');
        }

        return new Google([
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
            'redirectUri' => $this->getRedirectUri(),
        ]);
    }

    public function getAuthorizationUrl(): string
    {
        $provider = $this->getProvider();

        $authUrl = $provider->getAuthorizationUrl([
            'scope' => $this->config['scopes'],
            'access_type' => 'offline',
            'prompt' => 'consent',
            'include_granted_scopes' => 'true'
        ]);

        session(['oauth2state' => $provider->getState()]);

        return $authUrl;
    }

    public function handleCallback(string $code): array
    {
        $provider = $this->getProvider();

        $token = $provider->getAccessToken('authorization_code', [
            'code' => $code,
        ]);

        return [
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken(),
            'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
        ];
    }

    public function refreshToken(): ?array
    {
        if (!$this->service->refresh_token) {
            return null;
        }

        $provider = $this->getProvider();

        try {
            $token = $provider->getAccessToken('refresh_token', [
                'refresh_token' => $this->service->refresh_token,
            ]);

            return [
                'access_token' => $token->getToken(),
                'refresh_token' => $token->getRefreshToken() ?: $this->service->refresh_token,
                'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
            ];
        } catch (\Exception) {
            // \Log::error('Failed to refresh YouTube token: ' . $e->getMessage());
            return null;
        }
    }

    public function revokeToken(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = $this->getClient();
            $client->revokeToken();

            return true;
        } catch (\Exception) {
            // \Log::error('Failed to revoke YouTube token: ' . $e->getMessage());
            return false;
        }
    }

    public function testConnection(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = $this->getClient();

            $youtubeService = $this->createYouTubeService($client);

            // Simple test: get user's channel info
            $channels = $youtubeService->channels->listChannels('snippet', [
                'mine' => true
            ]);

            return !empty($channels->getItems());
        } catch (\Exception) {
            // \Log::error('YouTube connection test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get Google Client instance
     */
    public function getClient(): \Google_Client
    {
        $client = new \Google_Client();
        $client->setAccessToken($this->service->access_token);

        return $client;
    }

    /**
     * Get YouTube Service instance
     */
    public function getYouTubeService(): \Google\Service\YouTube
    {
        return $this->createYouTubeService($this->getClient());
    }

    /**
     * Create YouTube Service
     */
    protected function createYouTubeService(\Google_Client $client): \Google\Service\YouTube
    {
        return new \Google\Service\YouTube($client);
    }

    /**
     * Get user's channel information
     */
    public function getChannelInfo(): array
    {
        if (!$this->service->access_token) {
            throw new \Exception('No access token available');
        }

        try {
            $youtubeService = $this->getYouTubeService();

            $channels = $youtubeService->channels->listChannels('snippet,statistics', [
                'mine' => true
            ]);

            $items = $channels->getItems();
            if (empty($items)) {
                throw new \Exception('No channel found for this account');
            }

            $channel = $items[0];
            $snippet = $channel->getSnippet();
            $statistics = $channel->getStatistics();

            return [
                'id' => $channel->getId(),
                'title' => $snippet->getTitle(),
                'description' => $snippet->getDescription(),
                'thumbnail' => $snippet->getThumbnails()->getDefault()->getUrl(),
                'subscriber_count' => $statistics->getSubscriberCount(),
                'video_count' => $statistics->getVideoCount(),
                'view_count' => $statistics->getViewCount(),
                'published_at' => $snippet->getPublishedAt(),
            ];
        } catch (\Exception $e) {
            throw new \Exception('Failed to get channel info: ' . $e->getMessage());
        }
    }

    /**
     * Get user's videos
     */
    public function getVideos(int $maxResults = 25): array
    {
        if (!$this->service->access_token) {
            throw new \Exception('No access token available');
        }

        try {
            $youtubeService = $this->getYouTubeService();

            // First get the channel's uploads playlist
            $channels = $youtubeService->channels->listChannels('contentDetails', [
                'mine' => true
            ]);

            $items = $channels->getItems();
            if (empty($items)) {
                throw new \Exception('No channel found for this account');
            }

            $uploadsPlaylistId = $items[0]->getContentDetails()->getRelatedPlaylists()->getUploads();

            // Get videos from uploads playlist
            $playlistItems = $youtubeService->playlistItems->listPlaylistItems('snippet', [
                'playlistId' => $uploadsPlaylistId,
                'maxResults' => $maxResults
            ]);

            $videos = [];
            foreach ($playlistItems->getItems() as $item) {
                $snippet = $item->getSnippet();
                $videos[] = [
                    'id' => $snippet->getResourceId()->getVideoId(),
                    'title' => $snippet->getTitle(),
                    'description' => $snippet->getDescription(),
                    'thumbnail' => $snippet->getThumbnails()->getDefault()->getUrl(),
                    'published_at' => $snippet->getPublishedAt(),
                ];
            }

            return $videos;
        } catch (\Exception $e) {
            throw new \Exception('Failed to get videos: ' . $e->getMessage());
        }
    }
}
