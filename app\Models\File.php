<?php

namespace App\Models;

use App\Models\Language;
use Illuminate\Database\Eloquent\Model;
use LBCDev\OAuthManager\Models\OAuthService;

class File extends Model
{
    protected $fillable = [
        'nombre',
        'url',
        'oauth_service_id',
        'language_id',
        'orden',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function oauthService()
    {
        return $this->belongsTo(OAuthService::class);
    }

    public function getGoogleDriveIdAttribute(): ?string
    {
        if (!$this->url) {
            return null;
        }

        // Captura el ID en diferentes formatos comunes de Drive
        if (preg_match('/\/d\/([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        if (preg_match('/id=([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        if (preg_match('/https:\/\/drive\.google\.com\/uc\?export=download&id=([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
