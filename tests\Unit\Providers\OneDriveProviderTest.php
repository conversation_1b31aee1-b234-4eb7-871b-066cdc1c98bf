<?php

namespace LBCDev\OAuthManager\Tests\Unit\Providers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\OneDriveProvider;

class OneDriveProviderTest extends TestCase
{
    protected OneDriveProvider $provider;
    protected OAuthService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new OAuthService([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'slug' => 'test-onedrive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $this->provider = new OneDriveProvider($this->service);
    }

    public function test_can_get_authorization_url()
    {
        $url = $this->provider->getAuthorizationUrl();

        $this->assertStringContainsString('https://login.live.com', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('response_type=code', $url);
    }

    public function test_can_handle_callback()
    {
        // Test that the method exists and returns expected structure
        $this->assertTrue(method_exists($this->provider, 'handleCallback'));

        // We can't easily test the actual OAuth flow without real credentials
        // so we'll just verify the method signature and basic functionality
        $this->expectException(\Exception::class);
        $this->provider->handleCallback('invalid_code');
    }

    public function test_can_refresh_token()
    {
        // Test that the method exists
        $this->assertTrue(method_exists($this->provider, 'refreshToken'));

        // Test with valid refresh token - should attempt to refresh
        $result = $this->provider->refreshToken();

        // Without real credentials, this should return null or throw exception
        $this->assertNull($result);
    }

    public function test_refresh_token_returns_null_when_no_refresh_token()
    {
        $service = new OAuthService([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => null,
        ]);

        $provider = new OneDriveProvider($service);
        $result = $provider->refreshToken();

        $this->assertNull($result);
    }

    public function test_can_revoke_token()
    {
        // Test that the method exists
        $this->assertTrue(method_exists($this->provider, 'revokeToken'));

        // Test with access token - should attempt to revoke
        $result = $this->provider->revokeToken();

        // Without real credentials, this should return false
        $this->assertFalse($result);
    }

    public function test_revoke_token_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => null,
        ]);

        $provider = new OneDriveProvider($service);
        $result = $provider->revokeToken();

        $this->assertFalse($result);
    }

    public function test_can_test_connection()
    {
        // Test that the method exists
        $this->assertTrue(method_exists($this->provider, 'testConnection'));

        // Test with access token - should attempt to test connection
        $result = $this->provider->testConnection();

        // Without real credentials, this should return false
        $this->assertFalse($result);
    }

    public function test_test_connection_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'slug' => 'test-onedrive-no-token',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => null,
        ]);

        $provider = new OneDriveProvider($service);
        $result = $provider->testConnection();

        $this->assertFalse($result);
    }

    public function test_can_get_files()
    {
        // Test that the method exists
        $this->assertTrue(method_exists($this->provider, 'getFiles'));

        // Test with access token - should attempt to get files
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get OneDrive files');

        $this->provider->getFiles();
    }

    public function test_can_get_user_info()
    {
        // Test that the method exists
        $this->assertTrue(method_exists($this->provider, 'getUserInfo'));

        // Test with access token - should attempt to get user info
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get user info');

        $this->provider->getUserInfo();
    }

    public function test_throws_exception_when_missing_credentials()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing client credentials');

        $service = new OAuthService([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'credentials' => [
                'client_id' => null,
                'client_secret' => null,
            ],
        ]);

        $provider = new OneDriveProvider($service);
        $provider->getAuthorizationUrl();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}
