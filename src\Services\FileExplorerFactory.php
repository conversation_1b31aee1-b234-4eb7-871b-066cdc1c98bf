<?php

namespace LBC<PERSON>ev\OAuthFileExplorer\Services;

use LBC<PERSON>ev\OAuthFileExplorer\Contracts\FileExplorerInterface;
use <PERSON><PERSON><PERSON>ev\OAuthFileExplorer\Services\Explorers\GoogleDriveExplorer;
use L<PERSON><PERSON>ev\OAuthFileExplorer\Services\Explorers\DropboxExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use InvalidArgumentException;

class FileExplorerFactory
{
    /**
     * Create a file explorer instance based on the OAuth service type
     * 
     * @param OAuthService $oauthService The OAuth service to create explorer for
     * @return FileExplorerInterface The appropriate file explorer instance
     * @throws InvalidArgumentException If service type is not supported
     */
    public static function create(OAuthService $oauthService): FileExplorerInterface
    {
        return match ($oauthService->service_type) {
            'google_drive' => new GoogleDriveExplorer($oauthService),
            'dropbox' => new DropboxExplorer($oauthService),
            'onedrive' => self::createOneDriveExplorer($oauthService),
            'youtube' => self::createYouTubeExplorer($oauthService),
            'mega' => self::createMegaExplorer($oauthService),
            default => throw new InvalidArgumentException("Unsupported service type: {$oauthService->service_type}")
        };
    }

    /**
     * Create a file explorer by service type and optional service name
     * 
     * @param string $serviceType The service type (e.g., 'google_drive', 'dropbox')
     * @param string|null $serviceName Optional service name for multiple services of same type
     * @return FileExplorerInterface The appropriate file explorer instance
     * @throws InvalidArgumentException If service is not found or type not supported
     */
    public static function createByType(string $serviceType, ?string $serviceName = null): FileExplorerInterface
    {
        $query = OAuthService::where('service_type', $serviceType)
            ->where('is_active', true)
            ->whereNotNull('access_token');

        if ($serviceName) {
            $query->where('name', $serviceName);
        }

        $oauthService = $query->first();

        if (!$oauthService) {
            $message = $serviceName 
                ? "No active OAuth service found for type '{$serviceType}' with name '{$serviceName}'"
                : "No active OAuth service found for type '{$serviceType}'";
            throw new InvalidArgumentException($message);
        }

        return self::create($oauthService);
    }

    /**
     * Get all available service types that have registered explorers
     * 
     * @return array Array of supported service types
     */
    public static function getSupportedServiceTypes(): array
    {
        return [
            'google_drive',
            'dropbox',
            'onedrive',
            'youtube',
            'mega'
        ];
    }

    /**
     * Check if a service type is supported
     * 
     * @param string $serviceType The service type to check
     * @return bool True if supported, false otherwise
     */
    public static function isServiceTypeSupported(string $serviceType): bool
    {
        return in_array($serviceType, self::getSupportedServiceTypes());
    }

    /**
     * Get all active OAuth services that have supported explorers
     * 
     * @return \Illuminate\Database\Eloquent\Collection Collection of OAuthService models
     */
    public static function getAvailableServices()
    {
        return OAuthService::whereIn('service_type', self::getSupportedServiceTypes())
            ->where('is_active', true)
            ->whereNotNull('access_token')
            ->get();
    }

    /**
     * Create OneDrive explorer (placeholder for future implementation)
     */
    private static function createOneDriveExplorer(OAuthService $oauthService): FileExplorerInterface
    {
        // TODO: Implement OneDriveExplorer
        throw new InvalidArgumentException("OneDrive explorer not yet implemented");
    }

    /**
     * Create YouTube explorer (placeholder for future implementation)
     */
    private static function createYouTubeExplorer(OAuthService $oauthService): FileExplorerInterface
    {
        // TODO: Implement YouTubeExplorer
        throw new InvalidArgumentException("YouTube explorer not yet implemented");
    }

    /**
     * Create Mega explorer (placeholder for future implementation)
     */
    private static function createMegaExplorer(OAuthService $oauthService): FileExplorerInterface
    {
        // TODO: Implement MegaExplorer
        throw new InvalidArgumentException("Mega explorer not yet implemented");
    }
}
