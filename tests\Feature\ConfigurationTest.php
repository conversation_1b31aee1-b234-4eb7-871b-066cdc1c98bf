<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use LBCDev\OAuthManager\Tests\TestCase;

class ConfigurationTest extends TestCase
{
    public function test_default_configuration_structure()
    {
        $config = config('oauth-manager');

        $this->assertArrayHasKey('services', $config);
        $this->assertArrayHasKey('callback_route', $config);
        $this->assertArrayHasKey('middleware', $config);

        $this->assertArrayHasKey('google_drive', $config['services']);
        $this->assertEquals('oauth-manager.callback', $config['callback_route']);
        $this->assertEquals(['web'], $config['middleware']);
    }

    public function test_google_drive_service_configuration()
    {
        $googleDriveConfig = config('oauth-manager.services.google_drive');

        $this->assertArray<PERSON>as<PERSON>ey('name', $googleDriveConfig);
        $this->assertArrayHasKey('provider', $googleDriveConfig);
        $this->assertArrayHasKey('scopes', $googleDriveConfig);
        $this->assertArrayHasKey('fields', $googleDriveConfig);

        $this->assertEquals('Google Drive', $googleDriveConfig['name']);
        $this->assertIsArray($googleDriveConfig['scopes']);
        $this->assertNotEmpty($googleDriveConfig['scopes']);
    }
}
