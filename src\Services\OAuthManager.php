<?php

namespace LBCDev\OAuthManager\Services;

use Illuminate\Support\Carbon;
use LBCDev\OAuthManager\Models\OAuthService;

class OAuthManager
{
    public function getService(string $serviceType, string $name = null): ?OAuthService
    {
        $query = OAuthService::where('service_type', $serviceType)
            ->where('is_active', true);

        if ($name) {
            $query->where('name', $name);
        }

        return $query->first();
    }

    public function refreshTokenIfNeeded(OAuthService $service): bool
    {
        if (!$service->needsRefresh()) {
            return true;
        }

        $provider = $service->getProviderInstance();
        $tokenData = $provider->refreshToken();

        if (!$tokenData) {
            return false;
        }

        // Calcular expires_at si solo tenemos expires_in
        if (!isset($tokenData['expires_at']) && isset($tokenData['expires_in'])) {
            $tokenData['expires_at'] = Carbon::now()->addSeconds($tokenData['expires_in']);
        }

        $service->update([
            'access_token' => $tokenData['access_token'],
            'refresh_token' => $tokenData['refresh_token'],
            'expires_at' => $tokenData['expires_at'],
        ]);

        return true;
    }

    public function getValidToken(string $serviceType, string $name = null): ?string
    {
        $service = $this->getService($serviceType, $name);

        if (!$service) {
            return null;
        }

        if (!$this->refreshTokenIfNeeded($service)) {
            return null;
        }

        $service->update(['last_used_at' => now()]);

        return $service->access_token;
    }

    /**
     * Revoca el token de un servicio específico.
     */
    public function revokeToken(string $serviceType, string $name = null): bool
    {
        $service = $this->getService($serviceType, $name);

        if (!$service) {
            return false;
        }

        return $service->revokeToken();
    }

    /**
     * Revoca el token de un servicio por ID.
     */
    public function revokeTokenById(int $serviceId): bool
    {
        $service = OAuthService::find($serviceId);

        if (!$service) {
            return false;
        }

        return $service->revokeToken();
    }
}
