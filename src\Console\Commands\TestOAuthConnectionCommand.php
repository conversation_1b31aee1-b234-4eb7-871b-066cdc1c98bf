<?php

namespace LBCDev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class TestOAuthConnectionCommand extends Command
{
    protected $signature = 'oauth:test {service_id} {--refresh}';
    protected $description = 'Test OAuth connection for a service';

    public function handle(OAuthManager $oauthManager): void
    {
        $this->info('🔍 Probando conexiones...');

        $serviceId = $this->argument('service_id');
        $service = OAuthService::find($serviceId);

        if (!$service) {
            $this->error("❌ Servicio con ID {$serviceId} no encontrado");
            return;
        }

        $this->info("🔗 Probando conexión para: {$service->name} ({$service->service_type})");

        if (!$service->access_token) {
            $this->error('🚫 Servicio no autorizado. Por favor autorízalo primero.');
            return;
        }

        if ($service->isTokenExpired()) {
            $this->warn('⏰ Token expirado.');

            if ($this->option('refresh') && $service->refresh_token) {
                $this->info('♻️ Intentando refrescar token...');

                if ($oauthManager->refreshTokenIfNeeded($service)) {
                    $this->info('✅ Token refrescado con éxito!');
                } else {
                    $this->error('❌ No se pudo refrescar el token. Por favor reautoriza.');
                    return;
                }
            } else {
                $this->error('❌ Token expirado. Usa --refresh o reautoriza.');
                return;
            }
        }

        try {
            $provider = $service->getProviderInstance();

            // dd($service->getProviderInstance());

            if ($provider->testConnection()) {
                $this->info('✅ Conexión exitosa!');
            } else {
                $this->error('❌ La prueba de conexión falló');
            }
        } catch (\Exception $e) {
            $this->error('❌ Error en la prueba de conexión: ' . $e->getMessage());
        }
    }
}
