<?php

namespace <PERSON>BC<PERSON>ev\OAuthManager\Tests\Unit;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Services\OAuthManager;
use LBCDev\OAuthManager\Console\Commands\RefreshTokenCommand;
use LBCDev\OAuthManager\Console\Commands\TestOAuthConnectionCommand;

class OAuthManagerServiceProviderTest extends TestCase
{
    public function test_oauth_manager_is_registered_as_singleton()
    {
        $manager1 = app(OAuthManager::class);
        $manager2 = app(OAuthManager::class);

        $this->assertSame($manager1, $manager2);
    }

    public function test_commands_are_registered()
    {
        $kernel = app(\Illuminate\Contracts\Console\Kernel::class);

        $commands = Artisan::all();

        $this->assertArrayHasKey('oauth:test', $commands);
        $this->assertArray<PERSON>as<PERSON>ey('oauth:refresh', $commands);
    }

    public function test_config_is_merged()
    {
        $this->assertArrayHasKey('oauth-manager', config());
        $this->assertArrayHasKey('services', config('oauth-manager'));
    }

    public function test_routes_are_loaded()
    {
        $this->assertTrue(Route::has('oauth-manager.authorize'));
        $this->assertTrue(Route::has('oauth-manager.callback'));
    }
}
