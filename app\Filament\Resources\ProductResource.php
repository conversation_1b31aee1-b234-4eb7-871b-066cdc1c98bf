<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Enums\TaxonomyType;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationGroup = 'E-commerce';

    protected static null|int $navigationSort = 5;

    protected static ?string $navigationLabel = 'Productos';

    protected static ?string $modelLabel = 'Producto';

    protected static ?string $pluralModelLabel = 'Productos';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('nombre')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(
                                        fn(string $operation, $state, Forms\Set $set) =>
                                        $operation === 'create' ? $set('slug', Str::slug($state)) : null
                                    ),

                                Forms\Components\TextInput::make('slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(Product::class, 'slug', ignoreRecord: true)
                                    ->rules(['alpha_dash']),
                            ]),

                        Forms\Components\Textarea::make('descripcion_corta')
                            ->maxLength(500)
                            ->rows(3),

                        Forms\Components\RichEditor::make('descripcion_larga')
                            ->columnSpanFull(),
                    ]),

                Section::make('Precios')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('precio')
                                    ->required()
                                    ->numeric()
                                    ->prefix('$')
                                    ->step(0.01)
                                    ->minValue(0),

                                Forms\Components\TextInput::make('precio_descuento')
                                    ->numeric()
                                    ->prefix('$')
                                    ->step(0.01)
                                    ->minValue(0)
                                    ->lte('precio'),
                            ]),
                    ]),

                Section::make('Configuración')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('activo')
                                    ->default(true),

                                Forms\Components\TextInput::make('orden')
                                    ->numeric()
                                    ->default(0)
                                    ->minValue(0),
                            ]),
                    ]),

                Section::make('Categorización')
                    ->schema([
                        Forms\Components\Select::make('categories')
                            ->label('Categorías')
                            ->multiple()
                            ->relationship('taxonomies', 'name')
                            ->options(function () {
                                return Taxonomy::where('type', TaxonomyType::Category->value)
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('tags')
                            ->label('Etiquetas')
                            ->multiple()
                            ->options(function () {
                                return Taxonomy::where('type', TaxonomyType::Tag->value)
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->preload(),

                        // Forms\Components\Select::make('brands')
                        //     ->label('Marcas')
                        //     ->multiple()
                        //     ->options(function () {
                        //         return Taxonomy::where('type', 'brand')
                        //             ->orderBy('name')
                        //             ->pluck('name', 'id');
                        //     })
                        //     ->searchable()
                        //     ->preload(),

                        // Forms\Components\Select::make('colors')
                        //     ->label('Colores')
                        //     ->multiple()
                        //     ->options(function () {
                        //         return Taxonomy::where('type', TaxonomyType::Color->value)
                        //             ->orderBy('name')
                        //             ->pluck('name', 'id');
                        //     })
                        //     ->searchable()
                        //     ->preload(),

                        // Forms\Components\Select::make('sizes')
                        //     ->label('Tallas')
                        //     ->multiple()
                        //     ->options(function () {
                        //         return Taxonomy::where('type', TaxonomyType::Size->value)
                        //             ->orderBy('name')
                        //             ->pluck('name', 'id');
                        //     })
                        //     ->searchable()
                        //     ->preload(),

                        Forms\Components\Select::make('languages')
                            ->label('Idiomas')
                            ->multiple()
                            ->relationship('languages', 'name')
                            ->searchable()
                            ->preload(),
                    ]),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nombre')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('precio')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('precio_descuento')
                    ->money('USD')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('activo')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('orden')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('categories')
                    ->label('Categorías')
                    ->badge()
                    ->getStateUsing(function (Product $record) {
                        return $record->taxonomiesOfType(TaxonomyType::Category)->pluck('name')->toArray();
                    })
                    ->color('primary')
                    ->separator(','),

                Tables\Columns\TextColumn::make('tags')
                    ->label('Etiquetas')
                    ->badge()
                    ->getStateUsing(function (Product $record) {
                        return $record->taxonomiesOfType(TaxonomyType::Tag)->pluck('name')->toArray();
                    })
                    ->color('success')
                    ->separator(',')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('activo')
                    ->options([
                        '1' => 'Activo',
                        '0' => 'Inactivo',
                    ]),

                SelectFilter::make('categories')
                    ->label('Categorías')
                    ->relationship('taxonomies', 'name')
                    ->options(function () {
                        return Taxonomy::where('type', TaxonomyType::Category->value)
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload(),

                SelectFilter::make('tags')
                    ->label('Etiquetas')
                    ->relationship('taxonomies', 'name')
                    ->options(function () {
                        return Taxonomy::where('type', TaxonomyType::Tag->value)
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload(),

                Filter::make('precio_range')
                    ->form([
                        Forms\Components\TextInput::make('precio_min')
                            ->numeric()
                            ->placeholder('Precio mínimo'),
                        Forms\Components\TextInput::make('precio_max')
                            ->numeric()
                            ->placeholder('Precio máximo'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['precio_min'],
                                fn(Builder $query, $price): Builder => $query->where('precio', '>=', $price),
                            )
                            ->when(
                                $data['precio_max'],
                                fn(Builder $query, $price): Builder => $query->where('precio', '<=', $price),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('toggle_active')
                        ->label('Cambiar estado')
                        ->icon('heroicon-o-arrow-path')
                        ->action(function (Collection $records) {
                            $records->each(function (Product $record) {
                                $record->update(['activo' => !$record->activo]);
                            });
                        })
                        ->requiresConfirmation()
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('orden', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
