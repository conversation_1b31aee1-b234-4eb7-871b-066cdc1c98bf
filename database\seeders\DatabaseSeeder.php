<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Database\Seeders\CmsSeeder;
use Illuminate\Database\Seeder;
// use LBCDev\OAuthManager\Database\Seeders\OAuthServiceSeeder;
use Database\Seeders\CourseSeeder;
use Database\Seeders\TaxonomySeeder;
use Database\Seeders\OAuthServiceSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleYPermissionSeeder::class,
            UserSeeder::class,
            HomePageSectionSeeder::class,
            LanguageSeeder::class,
            FeatureSeeder::class,
            TestimonialSeeder::class,
            // ProductTaxonomySeeder::class,
            TaxonomySeeder::class,
            ProductSeeder::class,
            CourseSeeder::class,
            OAuthServiceSeeder::class,
            CmsSeeder::class,
        ]);
    }
}
