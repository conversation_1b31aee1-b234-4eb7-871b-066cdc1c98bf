<?php

namespace LBCDev\OAuthManager\Tests\Unit;

use LBC<PERSON>ev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class OAuthManagerRevokeTest extends TestCase
{
    use RefreshDatabase;

    private OAuthManager $manager;

    protected function setUp(): void
    {
        parent::setUp();
        $this->manager = new OAuthManager();
    }

    public function test_revoke_token_returns_false_when_service_not_found()
    {
        $result = $this->manager->revokeToken('nonexistent_type');

        $this->assertFalse($result);
    }

    public function test_revoke_token_returns_false_when_service_inactive()
    {
        OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'name' => 'Test Service',
            'is_active' => false,
            'access_token' => 'valid_token',
        ]);

        $result = $this->manager->revokeToken('google_drive', 'Test Service');

        $this->assertFalse($result);
    }

    public function test_revoke_token_successfully_revokes_by_type()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'valid_token',
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service->setProvider($mock);

        $result = $service->revokeToken();

        $this->assertTrue($result);

        // Verificar que los tokens fueron limpiados
        $service->refresh();
        $this->assertNull($service->access_token);
    }

    public function test_revoke_token_successfully_revokes_by_type_and_name()
    {
        $service1 = OAuthService::factory()->create([
            'name' => 'Service 1',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'valid_token_1',
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'Service 2',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'valid_token_2',
        ]);

        // Test que el manager puede obtener el servicio correcto
        $foundService = $this->manager->getService('google_drive', 'Service 2');
        $this->assertNotNull($foundService);
        $this->assertEquals('Service 2', $foundService->name);

        // Test revocación directa del servicio
        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service2->setProvider($mock);
        $result = $service2->revokeToken();

        $this->assertTrue($result);

        // Verificar que solo el servicio correcto fue afectado
        $service1->refresh();
        $service2->refresh();

        $this->assertNotNull($service1->access_token); // No afectado
        $this->assertNull($service2->access_token); // Revocado
    }

    public function test_revoke_token_by_id_returns_false_when_service_not_found()
    {
        $result = $this->manager->revokeTokenById(999);

        $this->assertFalse($result);
    }

    public function test_revoke_token_by_id_successfully_revokes()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service->setProvider($mock);

        // Test directo del servicio
        $result = $service->revokeToken();

        $this->assertTrue($result);

        $service->refresh();
        $this->assertNull($service->access_token);
    }

    public function test_revoke_token_by_id_handles_provider_failure()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(false);

        $service->setProvider($mock);

        $result = $service->revokeToken();

        $this->assertFalse($result);

        // Tokens should still be cleared locally
        $service->refresh();
        $this->assertNull($service->access_token);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
