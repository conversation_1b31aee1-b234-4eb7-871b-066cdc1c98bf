<?php

namespace LBCDev\OAuthManager\Tests\Feature\Http\Controllers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\GoogleDriveProvider;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class OAuthControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_authorize_redirects_to_oauth_provider()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('getAuthorizationUrl')
            ->andReturn('https://accounts.google.com/o/oauth2/auth?client_id=test');

        // 💡 Bind the mock into the container so getProviderInstance() picks it up
        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.authorize', $service));

        $response->assertRedirect('https://accounts.google.com/o/oauth2/auth?client_id=test');
    }

    public function test_callback_with_valid_code()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        // 💡 Clave para que el controlador use este mock
        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Service authorized successfully!');

        $service->refresh();
        $this->assertEquals('new_access_token', $service->access_token);
    }

    public function test_callback_with_error()
    {
        $service = OAuthService::factory()->create();

        $response = $this->get(route('oauth-manager.callback', $service) . '?error=access_denied&error_description=User denied access');

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization failed: User denied access');
    }

    public function test_callback_without_code()
    {
        $service = OAuthService::factory()->create();

        $response = $this->get(route('oauth-manager.callback', $service));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization code not received');
    }

    public function test_callback_handles_exception()
    {
        $service = OAuthService::factory()->create();

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->andThrow(new \Exception('OAuth error'));

        // Inyectar el mock en el contenedor para que el controlador lo use
        $this->app->instance(GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization failed: OAuth error');
    }

    public function test_redirect_uri_uses_fwd_host_when_enabled()
    {
        // Configurar el entorno para usar fwd.host
        config(['oauth-manager.services.google_drive.fields.use_fwd_service_for_local_oauth' => true]);

        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'slug' => 'test-service',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $provider = $service->getProviderInstance();

        // Usar reflexión para acceder al método protegido getRedirectUri
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('getRedirectUri');
        $method->setAccessible(true);

        $redirectUri = $method->invoke($provider);

        // Verificar que la URL contiene fwd.host
        $this->assertStringContainsString('https://fwd.host/', $redirectUri);
        $this->assertStringContainsString('/oauth-manager/callback/test-service', $redirectUri);
    }

    public function test_redirect_uri_normal_when_fwd_disabled()
    {
        // Configurar el entorno para NO usar fwd.host
        config(['oauth-manager.services.google_drive.fields.use_fwd_service_for_local_oauth' => false]);

        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'slug' => 'test-service',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $provider = $service->getProviderInstance();

        // Usar reflexión para acceder al método protegido getRedirectUri
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('getRedirectUri');
        $method->setAccessible(true);

        $redirectUri = $method->invoke($provider);

        // Verificar que la URL NO contiene fwd.host
        $this->assertStringNotContainsString('https://fwd.host/', $redirectUri);
        $this->assertStringContainsString('/oauth-manager/callback/test-service', $redirectUri);
    }

    public function test_authorize_stores_redirect_url_in_session()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('getAuthorizationUrl')
            ->andReturn('https://accounts.google.com/o/oauth2/auth?client_id=test');

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => '/admin/oauth-services'
        ]));

        $response->assertRedirect('https://accounts.google.com/o/oauth2/auth?client_id=test');
        $this->assertEquals('/admin/oauth-services', session('oauth_redirect_url'));
    }

    public function test_callback_redirects_to_session_url()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        // Simular URL de redirección en la sesión
        session(['oauth_redirect_url' => '/admin/oauth-services']);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect('/admin/oauth-services');
        $response->assertSessionHas('success', 'Service authorized successfully!');
        $this->assertNull(session('oauth_redirect_url')); // Debe ser removida después del uso
    }

    public function test_callback_redirects_to_config_default_url()
    {
        config(['oauth-manager.default_redirect_url' => '/dashboard']);

        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect('/dashboard');
        $response->assertSessionHas('success', 'Service authorized successfully!');
    }

    public function test_callback_redirects_to_request_parameter_url()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code&redirect_url=/custom-page');

        $response->assertRedirect('/custom-page');
        $response->assertSessionHas('success', 'Service authorized successfully!');
    }

    public function test_callback_redirect_priority_session_over_config()
    {
        config(['oauth-manager.default_redirect_url' => '/dashboard']);
        session(['oauth_redirect_url' => '/admin/oauth-services']);

        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        // Debe usar la URL de la sesión, no la de configuración
        $response->assertRedirect('/admin/oauth-services');
        $response->assertSessionHas('success', 'Service authorized successfully!');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
