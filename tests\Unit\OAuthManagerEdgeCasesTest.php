<?php

namespace LBCDev\OAuthManager\Tests\Unit;

use LBC<PERSON>ev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class OAuthManagerEdgeCasesTest extends TestCase
{
    use RefreshDatabase;

    public function test_refresh_token_if_needed_handles_null_expires_at()
    {
        $service = OAuthService::factory()->create([
            'expires_at' => null,
            'refresh_token' => 'valid_refresh_token',
        ]);

        $manager = new OAuthManager();

        // No debería intentar refrescar si expires_at es null
        $this->assertTrue($manager->refreshTokenIfNeeded($service));
    }

    public function test_refresh_token_if_needed_handles_provider_failure()
    {
        $service = OAuthService::factory()->create([
            'expires_at' => now()->subHour(),
            'refresh_token' => 'valid_refresh_token',
        ]);

        $mockProvider = Mockery::mock();
        $mockProvider->shouldReceive('refreshToken')->andReturn(null);

        $service->setProvider($mockProvider);

        $manager = new OAuthManager();

        $this->assertFalse($manager->refreshTokenIfNeeded($service));
    }

    public function test_get_valid_token_updates_last_used_at()
    {
        $service = OAuthService::factory()->create([
            'access_token' => 'valid_token',
            'expires_at' => now()->addHour(),
            'last_used_at' => null,
        ]);

        $manager = new OAuthManager();
        $token = $manager->getValidToken($service->service_type, $service->name);

        $service->refresh();

        $this->assertNotNull($token);
        $this->assertNotNull($service->last_used_at);
    }

    public function test_get_service_returns_null_for_inactive_service()
    {
        OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'name' => 'Test Service',
            'is_active' => false,
        ]);

        $manager = new OAuthManager();
        $service = $manager->getService('google_drive', 'Test Service');

        $this->assertNull($service);
    }
}
