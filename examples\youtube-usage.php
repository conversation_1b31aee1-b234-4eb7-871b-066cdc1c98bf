<?php

/**
 * Ejemplo de uso del YouTubeProvider
 * 
 * Este archivo muestra cómo usar el YouTubeProvider para:
 * - Crear un servicio OAuth de YouTube
 * - Autorizar el servicio
 * - Obtener tokens válidos
 * - Usar la API de YouTube
 */

require_once __DIR__ . '/../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

// 1. Crear un servicio OAuth de YouTube
$youtubeService = OAuthService::create([
    'name' => 'Mi canal de YouTube',
    'service_type' => 'youtube',
    'slug' => 'mi-youtube',
    'credentials' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
    ],
    'is_active' => true,
]);

echo "Servicio de YouTube creado con ID: " . $youtubeService->id . "\n";

// 2. Obtener URL de autorización
$provider = $youtubeService->getProviderInstance();
$authUrl = $provider->getAuthorizationUrl();

echo "URL de autorización: " . $authUrl . "\n";
echo "Visita esta URL para autorizar la aplicación.\n";

// 3. Después de la autorización, obtener token válido
$oauthManager = app(OAuthManager::class);

// Simular que ya tenemos un token (en la práctica, esto vendría del callback)
$youtubeService->update([
    'access_token' => 'token_de_ejemplo',
    'refresh_token' => 'refresh_token_de_ejemplo',
    'expires_at' => now()->addHours(1),
]);

// 4. Obtener token válido
$token = $oauthManager->getValidToken('youtube');

if ($token) {
    echo "Token válido obtenido: " . substr($token, 0, 20) . "...\n";
    
    // 5. Usar el provider directamente
    try {
        // Test de conexión
        $connectionTest = $provider->testConnection();
        echo "Test de conexión: " . ($connectionTest ? 'ÉXITO' : 'FALLO') . "\n";
        
        if ($connectionTest) {
            // 6. Obtener información del canal
            echo "\n=== INFORMACIÓN DEL CANAL ===\n";
            $channelInfo = $provider->getChannelInfo();
            
            echo "ID del canal: " . $channelInfo['id'] . "\n";
            echo "Título: " . $channelInfo['title'] . "\n";
            echo "Descripción: " . substr($channelInfo['description'], 0, 100) . "...\n";
            echo "Suscriptores: " . number_format($channelInfo['subscriber_count']) . "\n";
            echo "Videos: " . number_format($channelInfo['video_count']) . "\n";
            echo "Visualizaciones totales: " . number_format($channelInfo['view_count']) . "\n";
            echo "Fecha de creación: " . $channelInfo['published_at'] . "\n";
            echo "Thumbnail: " . $channelInfo['thumbnail'] . "\n";
            
            // 7. Obtener videos del canal
            echo "\n=== VIDEOS DEL CANAL (últimos 10) ===\n";
            $videos = $provider->getVideos(10);
            
            foreach ($videos as $index => $video) {
                echo ($index + 1) . ". " . $video['title'] . "\n";
                echo "   ID: " . $video['id'] . "\n";
                echo "   Descripción: " . substr($video['description'], 0, 80) . "...\n";
                echo "   Publicado: " . $video['published_at'] . "\n";
                echo "   Thumbnail: " . $video['thumbnail'] . "\n";
                echo "   URL: https://www.youtube.com/watch?v=" . $video['id'] . "\n\n";
            }
            
            // 8. Usar el cliente de YouTube directamente para operaciones avanzadas
            echo "\n=== USO DIRECTO DEL CLIENTE YOUTUBE ===\n";
            $youtubeService = $provider->getYouTubeService();
            
            // Ejemplo: Buscar videos públicos
            $searchResponse = $youtubeService->search->listSearch('snippet', [
                'q' => 'Laravel tutorial',
                'type' => 'video',
                'maxResults' => 5
            ]);
            
            echo "Resultados de búsqueda para 'Laravel tutorial':\n";
            foreach ($searchResponse->getItems() as $searchResult) {
                $snippet = $searchResult->getSnippet();
                echo "- " . $snippet->getTitle() . "\n";
                echo "  Canal: " . $snippet->getChannelTitle() . "\n";
                echo "  Publicado: " . $snippet->getPublishedAt() . "\n";
                echo "  URL: https://www.youtube.com/watch?v=" . $searchResult->getId()->getVideoId() . "\n\n";
            }
        }
        
    } catch (Exception $e) {
        echo "Error al usar la API de YouTube: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "No se pudo obtener un token válido.\n";
}

// 9. Ejemplo de renovación de token
echo "\n=== RENOVACIÓN DE TOKEN ===\n";
try {
    $refreshResult = $provider->refreshToken();
    if ($refreshResult) {
        echo "Token renovado exitosamente.\n";
        echo "Nuevo access_token: " . substr($refreshResult['access_token'], 0, 20) . "...\n";
    } else {
        echo "No se pudo renovar el token.\n";
    }
} catch (Exception $e) {
    echo "Error al renovar token: " . $e->getMessage() . "\n";
}

// 10. Ejemplo de revocación de token
echo "\n=== REVOCACIÓN DE TOKEN ===\n";
$revokeConfirm = readline("¿Deseas revocar el token? (y/N): ");
if (strtolower($revokeConfirm) === 'y') {
    try {
        $revokeResult = $provider->revokeToken();
        echo "Token revocado: " . ($revokeResult ? 'ÉXITO' : 'FALLO') . "\n";
        
        // Limpiar tokens localmente
        $youtubeService->update([
            'access_token' => null,
            'refresh_token' => null,
            'expires_at' => null,
        ]);
        
        echo "Tokens locales limpiados.\n";
    } catch (Exception $e) {
        echo "Error al revocar token: " . $e->getMessage() . "\n";
    }
} else {
    echo "Token no revocado.\n";
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para usar este ejemplo en tu aplicación:\n";
echo "1. Configura las credenciales de YouTube en tu .env\n";
echo "2. Crea el servicio OAuth usando el OAuthManager\n";
echo "3. Redirige al usuario a la URL de autorización\n";
echo "4. Maneja el callback para obtener los tokens\n";
echo "5. Usa los métodos del provider para interactuar con YouTube\n";

/**
 * Ejemplo de integración en un controlador Laravel:
 * 
 * class YouTubeController extends Controller
 * {
 *     public function connect()
 *     {
 *         $service = OAuthService::create([
 *             'name' => 'YouTube Channel',
 *             'service_type' => 'youtube',
 *             'slug' => 'youtube-' . auth()->id(),
 *             'credentials' => [
 *                 'client_id' => config('oauth-manager.services.youtube.fields.client_id'),
 *                 'client_secret' => config('oauth-manager.services.youtube.fields.client_secret'),
 *             ],
 *             'is_active' => true,
 *         ]);
 * 
 *         return redirect()->route('oauth-manager.authorize', ['service' => $service]);
 *     }
 * 
 *     public function dashboard()
 *     {
 *         $oauthManager = app(OAuthManager::class);
 *         $token = $oauthManager->getValidToken('youtube');
 * 
 *         if (!$token) {
 *             return redirect()->route('youtube.connect');
 *         }
 * 
 *         $service = OAuthService::where('service_type', 'youtube')
 *                                ->where('is_active', true)
 *                                ->first();
 * 
 *         $provider = $service->getProviderInstance();
 *         $channelInfo = $provider->getChannelInfo();
 *         $videos = $provider->getVideos(20);
 * 
 *         return view('youtube.dashboard', compact('channelInfo', 'videos'));
 *     }
 * }
 */
