<?php

namespace LBCDev\OAuthFileExplorer\Tests;

use Orchestra\Testbench\TestCase as Orchestra;
use LBC<PERSON>ev\OAuthFileExplorer\OAuthFileExplorerServiceProvider;
use LBCDev\OAuthManager\OAuthManagerServiceProvider;
use Livewire\LivewireServiceProvider;

class TestCase extends Orchestra
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->loadMigrationsFrom(__DIR__ . '/database/migrations');
    }

    protected function getPackageProviders($app)
    {
        return [
            LivewireServiceProvider::class,
            OAuthManagerServiceProvider::class,
            OAuthFileExplorerServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        config()->set('database.default', 'testing');
        config()->set('database.connections.testing', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // OAuth Manager configuration
        config()->set('oauth-manager.services', [
            'google_drive' => [
                'name' => 'Google Drive',
                'slug' => 'google-drive',
                'icon' => 'heroicon-o-cloud',
                'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
                'scopes' => ['https://www.googleapis.com/auth/drive.readonly'],
                'fields' => [
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret',
                ]
            ],
            'dropbox' => [
                'name' => 'Dropbox',
                'slug' => 'dropbox',
                'icon' => 'heroicon-o-cloud',
                'provider' => \LBCDev\OAuthManager\Providers\DropboxProvider::class,
                'scopes' => ['files.metadata.read'],
                'fields' => [
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret',
                ]
            ]
        ]);
    }

    protected function defineDatabaseMigrations()
    {
        $this->loadMigrationsFrom(__DIR__ . '/database/migrations');
    }
}
