<?php

namespace LBCDev\OAuthManager\Tests;

use Orchestra\Testbench\TestCase as BaseTestCase;
use Illuminate\Database\Eloquent\Factories\Factory;

abstract class TestCase extends BaseTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        Factory::guessFactoryNamesUsing(function (string $modelName) {
            return 'LBCDev\\OAuthManager\\Database\\Factories\\' . class_basename($modelName) . 'Factory';
        });
    }

    protected function getPackageProviders($app)
    {
        return [
            \LBCDev\OAuthManager\OAuthManagerServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        // Puedes definir configuración adicional si necesitas
        $app['config']->set('database.default', 'testing');
        $app['config']->set('database.connections.testing', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // Set up OAuth Manager configuration
        $app['config']->set('oauth-manager.services.google_drive', [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => [
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/drive.metadata.readonly'
            ],
            'fields' => [
                'client_id' => 'Client ID',
                'client_secret' => 'Client Secret',
            ]
        ]);

        $app['config']->set('oauth-manager.services.onedrive', [
            'name' => 'OneDrive',
            'slug' => 'onedrive',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\OneDriveProvider::class,
            'scopes' => [
                'https://graph.microsoft.com/Files.Read',
                'https://graph.microsoft.com/Files.ReadWrite',
                'https://graph.microsoft.com/User.Read'
            ],
            'fields' => [
                'client_id' => 'Client ID',
                'client_secret' => 'Client Secret',
            ]
        ]);
    }
}
