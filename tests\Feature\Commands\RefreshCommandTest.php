<?php

namespace LBCDev\OAuthManager\Tests\Feature\Commands;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use LBCDev\OAuthManager\Tests\TestCase;
use Mockery;

class RefreshCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->app['config']->set('oauth-manager.services.google_drive', [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'slug' => 'google-drive',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => ['https://www.googleapis.com/auth/drive'],
            'fields' => [
                'client_id' => 'Client ID',
                'client_secret' => 'Client Secret',
            ]
        ]);
    }

    public function test_refresh_all_command_outputs_success()
    {
        // Creamos un servicio con token vencido y refresh token válido
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh',
            'expires_at' => now()->subMinute(),
            'is_active' => true,
        ]);

        // Mock del OAuthManager
        $mockManager = Mockery::mock(OAuthManager::class);
        $mockManager->shouldReceive('refreshTokenIfNeeded')
            ->with(Mockery::on(function ($arg) use ($service) {
                return $arg->id === $service->id;
            }))
            ->once()
            ->andReturn(true);

        // Inyectar el mock en el contenedor
        $this->app->instance(OAuthManager::class, $mockManager);

        // Ejecutar el comando con --all
        $this->artisan('oauth:refresh --all')
            ->expectsOutput('Refreshing tokens for 1 services...')
            ->expectsOutput('Refreshing token for: Google Drive')
            ->expectsOutput('✅ Token refreshed for Google Drive')
            ->assertExitCode(0);
    }

    public function test_refresh_single_service_command()
    {
        // Creamos un servicio con token vencido
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh',
            'expires_at' => now()->subMinute(),
            'is_active' => true,
        ]);

        // Mock del OAuthManager
        $mockManager = Mockery::mock(OAuthManager::class);
        $mockManager->shouldReceive('refreshTokenIfNeeded')
            ->with(Mockery::on(function ($arg) use ($service) {
                return $arg->id === $service->id;
            }))
            ->once()
            ->andReturn(true);

        // Inyectar el mock en el contenedor
        $this->app->instance(OAuthManager::class, $mockManager);

        // Ejecutar el comando con el ID del servicio
        $this->artisan("oauth:refresh {$service->id}")
            ->expectsOutput('Refreshing token for: Google Drive')
            ->expectsOutput('✅ Token refreshed successfully!')
            ->assertExitCode(0);
    }

    public function test_refresh_command_failed_refresh()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh',
            'expires_at' => now()->subMinute(),
            'is_active' => true,
        ]);

        // Mock que retorna false (fallo en refresh)
        $mockManager = Mockery::mock(OAuthManager::class);
        $mockManager->shouldReceive('refreshTokenIfNeeded')
            ->once()
            ->andReturn(false);

        $this->app->instance(OAuthManager::class, $mockManager);

        $this->artisan('oauth:refresh --all')
            ->expectsOutput('Refreshing tokens for 1 services...')
            ->expectsOutput('Refreshing token for: Google Drive')
            ->expectsOutput('❌ Failed to refresh token for Google Drive')
            ->assertExitCode(0);
    }

    public function test_refresh_command_no_services_found()
    {
        $this->artisan('oauth:refresh --all')
            ->expectsOutput('No services with refresh tokens found')
            ->assertExitCode(0);
    }

    public function test_refresh_single_service_not_found()
    {
        $this->artisan('oauth:refresh 999')
            ->expectsOutput('Service with ID 999 not found')
            ->assertExitCode(0);
    }

    public function test_refresh_single_service_no_refresh_token()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'refresh_token' => null,
        ]);

        $this->artisan("oauth:refresh {$service->id}")
            ->expectsOutput('Service does not have a refresh token')
            ->assertExitCode(0);
    }
}
