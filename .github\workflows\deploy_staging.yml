name: Deployment to Staging

on:
  push:
    branches: [staging]
  # pull_request:
  #    branches: [staging]

jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: [8.3]
    name: Test con PHP ${{ matrix.php }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Cache PHP dependencies
        uses: actions/cache@v3
        with:
          path: vendor
          key: dependencies-php-${{ matrix.php }}-composer-${{ hashFiles('**/composer.lock') }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, gd
          coverage: none

      - name: Copy ENV Laravel Configuration for CI
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"

      - name: Add github token to the global composer auth config
        run: composer config --global github-oauth.github.com ${{ secrets.PAS_TOKEN_GITHUB_FOR_PRIVATE_REPOS }}

      - name: Install dependencies
        run: composer install --no-ansi --no-interaction --no-scripts --no-suggest --no-progress --prefer-dist

      - name: Install NPM dependencies
        run: npm install

      - name: Compile assets
        run: npm run build

      - name: Generate key
        run: php artisan key:generate

      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache

      - name: Create Database
        run: |
          mkdir -p database
          touch database/database.sqlite

      - name: Execute tests (Unit and Feature tests) via PHPUnit
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
          CI: true
          APP_ENV: testing
          APP_DEBUG: true
        run: php artisan test

  deploy:
    runs-on: ubuntu-latest
    needs: tests
    strategy:
      matrix:
        php: [8.3]
    name: Deploy to staging with PHP ${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Cache PHP dependencies
        uses: actions/cache@v3
        with:
          path: vendor
          key: dependencies-php-${{ matrix.php }}-composer-${{ hashFiles('**/composer.lock') }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, gd
          coverage: none

      - name: Copy ENV Laravel Configuration for CI
        run: php -r "file_put_contents(__DIR__ . '/.env', '${{ secrets.STAGING_DOT_ENV }}');"

      - name: Add github token to the global composer auth config
        run: composer config --global github-oauth.github.com ${{ secrets.PAS_TOKEN_GITHUB_FOR_PRIVATE_REPOS }}

      - name: Install dependencies
        run: composer install --no-ansi --no-interaction --no-scripts --no-suggest --no-progress --prefer-dist

      - name: Install NPM dependencies
        run: npm install

      - name: Compile assets
        run: npm run build

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.STAGING_SERVER_PRIVATE_SSH_KEY }}
          name: vps-ssh-key
          config: |
            Host target
              Hostname ${{ secrets.STAGING_SSH_HOST }}
              Port ${{ secrets.STAGING_SSH_PORT }}
              User ${{ secrets.STAGING_SSH_USER }}
              IdentityFile ~/.ssh/vps-ssh-key
              StrictHostKeyChecking yes
          known_hosts: ${{ secrets.STAGING_KNOWN_HOSTS }}

      #  - name: Debug SSH Key
      #    run: ssh -v -p ${{ secrets.STAGING_SSH_PORT }} -i ~/.ssh/vps-ssh-key ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_SSH_HOST }}

      - name: Sync Files to Server
        run: |
          rsync -r --delete \
            --exclude 'node_modules' \
            --exclude 'storage/backups' \
            --exclude 'storage/app' \
            --exclude 'storage/debugbar' \
            --exclude 'storage/framework' \
            --exclude 'storage/logs' \
            --exclude 'storage/media-library' \
            --exclude 'public/storage' \
            --chmod=D2775,F664 --chown=${{ secrets.STAGING_SSH_USER }}:www-data \
            -e "ssh -p ${{ secrets.STAGING_SSH_PORT }} -i ~/.ssh/vps-ssh-key" \
            ./ ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_SSH_HOST }}:${{ secrets.STAGING_SERVER_DESTINATION }}

      #  - name: Ensure Required Directories Exist
      #    run: |
      #       ssh -p ${{ secrets.STAGING_SSH_PORT }} -i ~/.ssh/vps-ssh-key \
      #       ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_SSH_HOST }} \
      #       "for dir in \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/app/public \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/app/private \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/framework/cache/data \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/framework/sessions \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/framework/testing \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/framework/views \
      #       ${{ secrets.STAGING_SERVER_DESTINATION }}/storage/log; \
      #       do if [ ! -d \"\$dir\" ]; then mkdir -p \"\$dir\" && \
      #       chmod 2775 \"\$dir\" && \
      #       chown ${{ secrets.STAGING_SSH_USER }}:www-data \"\$dir\"; fi; done"
