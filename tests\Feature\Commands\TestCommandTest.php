<?php

namespace LBCDev\OAuthManager\Tests\Feature\Commands;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Tests\TestCase;
use Mockery;
use Symfony\Component\Console\Output\BufferedOutput;

class TestCommandTest extends TestCase
{
    use RefreshDatabase;


    public function test_test_command_outputs_connection_success()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
            'expires_at' => now()->addHour(),
        ]);

        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('setProvider')
            ->with(Mockery::type(\LBCDev\OAuthManager\Models\OAuthService::class))
            ->andReturnSelf();
        $mock->shouldReceive('testConnection')->once()->andReturnTrue();

        // Registrar mock en contenedor
        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mock);

        $output = new \Symfony\Component\Console\Output\BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:test', [
            'service_id' => $service->id,
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('✅ Conexión exitosa!', $display);
    }




    // public function test_test_command_outputs_connection_success()
    // {
    //     $service = OAuthService::factory()->create([
    //         'name' => 'Google Drive',
    //         'slug' => 'google-drive',
    //         'service_type' => 'google_drive',
    //         'access_token' => 'valid_token',
    //         'expires_at' => now()->addHour(),
    //     ]);

    //     $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
    //     $mock->shouldReceive('testConnection')
    //         ->once()
    //         ->andReturnTrue();

    //     $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mock);

    //     $service->setProvider($mock);

    //     $this->artisan('oauth:test', ['service_id' => $service->id])
    //         ->expectsOutput('🔍 Probando conexiones...')
    //         ->expectsOutput("🔗 Probando conexión para: Google Drive (google_drive)")
    //         ->expectsOutput('✅ Conexión exitosa!')
    //         ->assertExitCode(0);
    // }
}
