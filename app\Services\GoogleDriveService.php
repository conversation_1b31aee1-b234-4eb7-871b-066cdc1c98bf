<?php

namespace App\Services;

use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Facades\OAuthManager;
use Google\Service\Drive;
use Google\Service\Drive\DriveFile;

class GoogleDriveService
{
    protected $driveService;
    protected $oauthService;

    public function __construct(?OAuthService $oauthService = null)
    {
        $this->oauthService = $oauthService;
        if ($oauthService) {
            $this->initializeDriveService();
        }
    }

    /**
     * Initialize Google Drive service with OAuth token
     */
    protected function initializeDriveService(): void
    {
        if (!$this->oauthService || !$this->oauthService->access_token) {
            throw new \Exception('OAuth service not configured or no access token available');
        }

        $provider = $this->oauthService->getProviderInstance();
        $client = $provider->getClient();
        $this->driveService = new Drive($client);
    }

    /**
     * Set OAuth service and reinitialize Drive service
     */
    public function setOAuthService(OAuthService $oauthService): self
    {
        $this->oauthService = $oauthService;
        $this->initializeDriveService();
        return $this;
    }

    /**
     * List files and folders in a specific folder
     */
    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        if (!$this->driveService) {
            throw new \Exception('Drive service not initialized');
        }

        try {
            $optParams = [
                'pageSize' => $pageSize,
                'fields' => 'nextPageToken, files(id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink)',
                'q' => "'{$folderId}' in parents and trashed = false",
                'orderBy' => 'folder,name'
            ];

            $results = $this->driveService->files->listFiles($optParams);
            $files = $results->getFiles();

            $formattedFiles = [];
            foreach ($files as $file) {
                $formattedFiles[] = $this->formatFileData($file);
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $results->getNextPageToken()
            ];
        } catch (\Exception $e) {
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    /**
     * Get file information by ID
     */
    public function getFile(string $fileId): array
    {
        if (!$this->driveService) {
            throw new \Exception('Drive service not initialized');
        }

        try {
            $file = $this->driveService->files->get($fileId, [
                'fields' => 'id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink'
            ]);

            return $this->formatFileData($file);
        } catch (\Exception $e) {
            throw new \Exception('Error getting file: ' . $e->getMessage());
        }
    }

    /**
     * Get folder breadcrumb path
     */
    public function getBreadcrumb(string $folderId): array
    {
        if (!$this->driveService || $folderId === 'root') {
            return [['id' => 'root', 'name' => 'My Drive']];
        }

        try {
            $breadcrumb = [];
            $currentId = $folderId;

            while ($currentId !== 'root') {
                $file = $this->driveService->files->get($currentId, ['fields' => 'id, name, parents']);
                
                array_unshift($breadcrumb, [
                    'id' => $file->getId(),
                    'name' => $file->getName()
                ]);

                $parents = $file->getParents();
                $currentId = $parents ? $parents[0] : 'root';
            }

            array_unshift($breadcrumb, ['id' => 'root', 'name' => 'My Drive']);

            return $breadcrumb;
        } catch (\Exception $e) {
            return [['id' => 'root', 'name' => 'My Drive']];
        }
    }

    /**
     * Search files by name
     */
    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        if (!$this->driveService) {
            throw new \Exception('Drive service not initialized');
        }

        try {
            $searchQuery = "name contains '{$query}' and trashed = false";
            if ($folderId !== 'root') {
                $searchQuery .= " and '{$folderId}' in parents";
            }

            $optParams = [
                'pageSize' => 50,
                'fields' => 'files(id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink)',
                'q' => $searchQuery,
                'orderBy' => 'folder,name'
            ];

            $results = $this->driveService->files->listFiles($optParams);
            $files = $results->getFiles();

            $formattedFiles = [];
            foreach ($files as $file) {
                $formattedFiles[] = $this->formatFileData($file);
            }

            return $formattedFiles;
        } catch (\Exception $e) {
            throw new \Exception('Error searching files: ' . $e->getMessage());
        }
    }

    /**
     * Format file data for consistent output
     */
    protected function formatFileData(DriveFile $file): array
    {
        $isFolder = $file->getMimeType() === 'application/vnd.google-apps.folder';
        
        return [
            'id' => $file->getId(),
            'name' => $file->getName(),
            'mimeType' => $file->getMimeType(),
            'isFolder' => $isFolder,
            'size' => $file->getSize() ? (int) $file->getSize() : 0,
            'sizeFormatted' => $file->getSize() ? $this->formatFileSize((int) $file->getSize()) : '-',
            'modifiedTime' => $file->getModifiedTime(),
            'webViewLink' => $file->getWebViewLink(),
            'webContentLink' => $file->getWebContentLink(),
            'iconLink' => $file->getIconLink(),
            'parents' => $file->getParents() ?: [],
            'downloadUrl' => $isFolder ? null : $file->getWebContentLink()
        ];
    }

    /**
     * Format file size in human readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);

        $size = $bytes / pow(1024, $power);
        $formattedSize = round($size, 2);

        return $formattedSize . ' ' . $units[$power];
    }

    /**
     * Test if the service is properly configured and working
     */
    public function testConnection(): bool
    {
        try {
            if (!$this->driveService) {
                return false;
            }

            $this->driveService->about->get(['fields' => 'user']);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
