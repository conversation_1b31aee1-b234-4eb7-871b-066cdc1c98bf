<?php

namespace LBCDev\OAuthManager\Tests\Unit\Providers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\MegaProvider;

class MegaProviderTest extends TestCase
{
    protected MegaProvider $provider;
    protected OAuthService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'slug' => 'test-mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => 'test_session_token',
            'refresh_token' => null,
        ]);

        $this->provider = new MegaProvider($this->service);
    }

    public function test_can_get_authorization_url()
    {
        $url = $this->provider->getAuthorizationUrl();

        $this->assertStringContainsString('/oauth/mega/auth', $url);
        $this->assertStringContainsString('service=test-mega', $url);
        $this->assertStringContainsString('state=', $url);
    }

    public function test_can_handle_callback_with_valid_credentials()
    {
        // Test que el método existe
        $this->assertTrue(method_exists($this->provider, 'handleCallback'));

        // Mock de las credenciales codificadas
        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'test_password'
        ];
        $encodedCredentials = base64_encode(json_encode($credentials));

        // Con la implementación simulada, debería funcionar
        $result = $this->provider->handleCallback($encodedCredentials);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertNull($result['refresh_token']);
        $this->assertNull($result['expires_at']);
    }

    public function test_handle_callback_fails_with_invalid_code()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid Mega credentials in callback');

        $this->provider->handleCallback('invalid_code');
    }

    public function test_can_refresh_token_with_valid_credentials()
    {
        // Test que el método existe
        $this->assertTrue(method_exists($this->provider, 'refreshToken'));

        // Con la implementación simulada, debería funcionar
        $result = $this->provider->refreshToken();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertNull($result['refresh_token']);
        $this->assertNull($result['expires_at']);
    }

    public function test_refresh_token_returns_null_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => null,
        ]);

        $provider = new MegaProvider($service);
        $result = $provider->refreshToken();

        $this->assertNull($result);
    }

    public function test_refresh_token_returns_null_when_missing_credentials()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                // password missing
            ],
            'access_token' => 'test_token',
        ]);

        $provider = new MegaProvider($service);
        $result = $provider->refreshToken();

        $this->assertNull($result);
    }

    public function test_can_revoke_token()
    {
        // Test that the method exists and returns true
        $result = $this->provider->revokeToken();
        $this->assertTrue($result);
    }

    public function test_revoke_token_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => null,
        ]);

        $provider = new MegaProvider($service);
        $result = $provider->revokeToken();

        $this->assertFalse($result);
    }

    public function test_can_test_connection()
    {
        // Test que el método existe
        $this->assertTrue(method_exists($this->provider, 'testConnection'));

        // Con la implementación simulada, debería funcionar
        $result = $this->provider->testConnection();
        $this->assertTrue($result);
    }

    public function test_test_connection_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => null,
        ]);

        $provider = new MegaProvider($service);
        $result = $provider->testConnection();

        $this->assertFalse($result);
    }

    public function test_can_get_user_info()
    {
        // Test que el método existe
        $this->assertTrue(method_exists($this->provider, 'getUserInfo'));

        // Con la implementación simulada, debería funcionar
        $result = $this->provider->getUserInfo();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('storage_used', $result);
        $this->assertArrayHasKey('file_count', $result);
        $this->assertEquals('<EMAIL>', $result['email']);
    }

    public function test_get_user_info_throws_exception_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => null,
        ]);

        $provider = new MegaProvider($service);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No access token available');

        $provider->getUserInfo();
    }

    public function test_can_get_files()
    {
        // Test que el método existe
        $this->assertTrue(method_exists($this->provider, 'getFiles'));

        // Con la implementación simulada, debería funcionar
        $result = $this->provider->getFiles();

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Verificar estructura de los archivos
        $firstFile = $result[0];
        $this->assertArrayHasKey('id', $firstFile);
        $this->assertArrayHasKey('name', $firstFile);
        $this->assertArrayHasKey('type', $firstFile);
        $this->assertArrayHasKey('size', $firstFile);
    }

    public function test_get_files_throws_exception_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Mega',
            'service_type' => 'mega',
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'test_password',
            ],
            'access_token' => null,
        ]);

        $provider = new MegaProvider($service);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No access token available');

        $provider->getFiles();
    }
}
