{"name": "lbcdev/oauth-file-explorer", "description": "Extensible OAuth file explorer component for Livewire with support for multiple cloud storage services", "type": "library", "license": "MIT", "authors": [{"name": "LBCDev", "email": "<EMAIL>"}], "require": {"php": "^8.2", "livewire/livewire": "^3.0", "lbcdev/oauth-manager": "dev-main"}, "require-dev": {"orchestra/testbench": "^10.0", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"LBCDev\\OAuthFileExplorer\\": "src/"}}, "autoload-dev": {"psr-4": {"LBCDev\\OAuthFileExplorer\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["LBCDev\\OAuthFileExplorer\\OAuthFileExplorerServiceProvider"]}}, "repositories": [{"type": "vcs", "url": "https://github.com/Luinux81/laravel-oauth-manager"}], "minimum-stability": "dev", "prefer-stable": true}