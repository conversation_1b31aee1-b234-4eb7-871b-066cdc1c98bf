<?php

namespace LBCDev\OAuthManager\Tests\Unit\Providers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\DropboxProvider;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Psr7\Response;
use Spatie\Dropbox\Client as DropboxClient;
use Mockery;

class DropboxProviderTest extends TestCase
{
    protected DropboxProvider $provider;
    protected OAuthService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'slug' => 'test-dropbox',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $this->provider = new DropboxProvider($this->service);
    }

    public function test_get_authorization_url()
    {
        $url = $this->provider->getAuthorizationUrl();

        $this->assertStringContainsString('https://www.dropbox.com/oauth2/authorize', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('response_type=code', $url);
        $this->assertStringContainsString('token_access_type=offline', $url);
    }

    public function test_get_authorization_url_throws_exception_without_client_id()
    {
        $service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'credentials' => [],
        ]);

        $provider = new DropboxProvider($service);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing client_id in credentials');

        $provider->getAuthorizationUrl();
    }

    public function test_handle_callback_success()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockResponse = new Response(200, [], json_encode([
            'access_token' => 'new_access_token',
            'refresh_token' => 'new_refresh_token',
            'expires_in' => 3600,
        ]));

        $mockClient->shouldReceive('post')
            ->once()
            ->with('https://api.dropboxapi.com/oauth2/token', Mockery::type('array'))
            ->andReturn($mockResponse);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->handleCallback('test_code');

        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('expires_at', $result);
        $this->assertEquals('new_access_token', $result['access_token']);
        $this->assertEquals('new_refresh_token', $result['refresh_token']);
    }

    public function test_handle_callback_throws_exception_without_credentials()
    {
        $service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'credentials' => [],
        ]);

        $provider = new DropboxProvider($service);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing client credentials');

        $provider->handleCallback('test_code');
    }

    public function test_refresh_token_success()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockResponse = new Response(200, [], json_encode([
            'access_token' => 'refreshed_access_token',
            'refresh_token' => 'refreshed_refresh_token',
            'expires_in' => 3600,
        ]));

        $mockClient->shouldReceive('post')
            ->once()
            ->with('https://api.dropboxapi.com/oauth2/token', Mockery::type('array'))
            ->andReturn($mockResponse);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->refreshToken();

        $this->assertIsArray($result);
        $this->assertEquals('refreshed_access_token', $result['access_token']);
        $this->assertEquals('refreshed_refresh_token', $result['refresh_token']);
    }

    public function test_refresh_token_returns_null_when_no_refresh_token()
    {
        $service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => null,
        ]);

        $provider = new DropboxProvider($service);
        $result = $provider->refreshToken();

        $this->assertNull($result);
    }

    public function test_refresh_token_failure()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockClient->shouldReceive('post')
            ->andThrow(new \Exception('Token refresh failed'));

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->refreshToken();

        $this->assertNull($result);
    }

    public function test_revoke_token_success()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockResponse = new Response(200);

        $mockClient->shouldReceive('post')
            ->once()
            ->with('https://api.dropboxapi.com/2/auth/token/revoke', Mockery::type('array'))
            ->andReturn($mockResponse);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->revokeToken();

        $this->assertTrue($result);
    }

    public function test_revoke_token_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => null,
        ]);

        $provider = new DropboxProvider($service);
        $result = $provider->revokeToken();

        $this->assertFalse($result);
    }

    public function test_revoke_token_failure()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockClient->shouldReceive('post')
            ->andThrow(new \Exception('Revoke failed'));

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->revokeToken();

        $this->assertFalse($result);
    }

    public function test_test_connection_success()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockResponse = new Response(200, [], json_encode([
            'account_id' => 'test_account_id',
            'name' => ['display_name' => 'Test User'],
        ]));

        $mockClient->shouldReceive('post')
            ->once()
            ->with('https://api.dropboxapi.com/2/users/get_current_account', Mockery::type('array'))
            ->andReturn($mockResponse);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->testConnection();

        $this->assertTrue($result);
    }

    public function test_test_connection_returns_false_when_no_access_token()
    {
        $service = new OAuthService([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => null,
        ]);

        $provider = new DropboxProvider($service);
        $result = $provider->testConnection();

        $this->assertFalse($result);
    }

    public function test_test_connection_failure()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockClient->shouldReceive('post')
            ->andThrow(new \Exception('Connection failed'));

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->testConnection();

        $this->assertFalse($result);
    }

    public function test_get_files_success()
    {
        $mockDropboxClient = Mockery::mock(DropboxClient::class);
        $mockDropboxClient->shouldReceive('listFolder')
            ->once()
            ->with('')
            ->andReturn([
                'entries' => [
                    ['name' => 'file1.txt', '.tag' => 'file'],
                    ['name' => 'folder1', '.tag' => 'folder'],
                ]
            ]);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockDropboxClient($mockDropboxClient);

        $result = $fakeProvider->getFiles();

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
    }

    public function test_get_user_info_success()
    {
        $mockClient = Mockery::mock(Client::class);
        $mockResponse = new Response(200, [], json_encode([
            'account_id' => 'test_account_id',
            'name' => ['display_name' => 'Test User'],
            'email' => '<EMAIL>',
        ]));

        $mockClient->shouldReceive('post')
            ->once()
            ->with('https://api.dropboxapi.com/2/users/get_current_account', Mockery::type('array'))
            ->andReturn($mockResponse);

        $fakeProvider = new FakeDropboxProvider($this->service);
        $fakeProvider->setMockClient($mockClient);

        $result = $fakeProvider->getUserInfo();

        $this->assertIsArray($result);
        $this->assertEquals('test_account_id', $result['account_id']);
        $this->assertEquals('Test User', $result['name']['display_name']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

class FakeDropboxProvider extends DropboxProvider
{
    protected $mockClient;
    protected $mockDropboxClient;

    public function setMockClient($client)
    {
        $this->mockClient = $client;
    }

    public function setMockDropboxClient($dropboxClient)
    {
        $this->mockDropboxClient = $dropboxClient;
    }

    public function handleCallback(string $code): array
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            throw new \Exception('Missing client credentials');
        }

        try {
            $client = $this->mockClient ?? new \GuzzleHttp\Client();

            $response = $client->post('https://api.dropboxapi.com/oauth2/token', [
                'form_params' => [
                    'code' => $code,
                    'grant_type' => 'authorization_code',
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                    'redirect_uri' => $this->getRedirectUri(),
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'access_token' => $data['access_token'],
                'refresh_token' => $data['refresh_token'] ?? null,
                'expires_at' => isset($data['expires_in']) ? now()->addSeconds($data['expires_in']) : null,
            ];
        } catch (\Exception $e) {
            throw new \Exception('Failed to exchange code for token: ' . $e->getMessage());
        }
    }

    public function refreshToken(): ?array
    {
        if (!$this->service->refresh_token) {
            return null;
        }

        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            return null;
        }

        try {
            $client = $this->mockClient ?? new \GuzzleHttp\Client();

            $response = $client->post('https://api.dropboxapi.com/oauth2/token', [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $this->service->refresh_token,
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'access_token' => $data['access_token'],
                'refresh_token' => $data['refresh_token'] ?? $this->service->refresh_token,
                'expires_at' => isset($data['expires_in']) ? now()->addSeconds($data['expires_in']) : null,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    public function revokeToken(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = $this->mockClient ?? new \GuzzleHttp\Client();

            $response = $client->post('https://api.dropboxapi.com/2/auth/token/revoke', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function testConnection(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = $this->mockClient ?? new \GuzzleHttp\Client();

            // Test connection by getting current account info
            $response = $client->post('https://api.dropboxapi.com/2/users/get_current_account', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    protected function getDropboxClient(): \Spatie\Dropbox\Client
    {
        if ($this->mockDropboxClient) {
            return $this->mockDropboxClient;
        }

        return parent::getDropboxClient();
    }

    public function getFiles(string $path = ''): array
    {
        try {
            $dropboxClient = $this->getDropboxClient();

            $response = $dropboxClient->listFolder($path);

            return $response['entries'] ?? [];
        } catch (\Exception $e) {
            throw new \Exception('Failed to get Dropbox files: ' . $e->getMessage());
        }
    }

    public function getUserInfo(): array
    {
        if (!$this->service->access_token) {
            throw new \Exception('No access token available');
        }

        try {
            $client = $this->mockClient ?? new \GuzzleHttp\Client();

            $response = $client->post('https://api.dropboxapi.com/2/users/get_current_account', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $e) {
            throw new \Exception('Failed to get user info: ' . $e->getMessage());
        }
    }
}
