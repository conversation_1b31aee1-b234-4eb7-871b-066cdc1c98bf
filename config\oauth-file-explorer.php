<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Explorer Settings
    |--------------------------------------------------------------------------
    */
    'default_page_size' => 50,
    
    /*
    |--------------------------------------------------------------------------
    | Supported Services
    |--------------------------------------------------------------------------
    */
    'supported_services' => [
        'google_drive',
        'dropbox',
        'onedrive',
        'youtube',
        'mega',
    ],

    /*
    |--------------------------------------------------------------------------
    | Explorer Classes
    |--------------------------------------------------------------------------
    */
    'explorers' => [
        'google_drive' => \LBCDev\OAuthFileExplorer\Services\Explorers\GoogleDriveExplorer::class,
        'dropbox' => \LBCDev\OAuthFileExplorer\Services\Explorers\DropboxExplorer::class,
        // Add more explorers here
    ],

    /*
    |--------------------------------------------------------------------------
    | File Type Filters
    |--------------------------------------------------------------------------
    */
    'mime_types' => [
        'documents' => [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            // Google Workspace files
            'application/vnd.google-apps.document',
            'application/vnd.google-apps.spreadsheet',
            'application/vnd.google-apps.presentation',
        ],
        'images' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/svg+xml',
            'image/webp'
        ],
        'videos' => [
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv'
        ],
        'audio' => [
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
            'audio/flac',
            'audio/wma'
        ],
        'archives' => [
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'application/x-tar',
            'application/gzip'
        ],
    ],
];
