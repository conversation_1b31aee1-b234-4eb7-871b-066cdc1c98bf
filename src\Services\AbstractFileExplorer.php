<?php

namespace LBCDev\OAuthFileExplorer\Services;

use LBCDev\OAuthFileExplorer\Contracts\FileExplorerInterface;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

abstract class AbstractFileExplorer implements FileExplorerInterface
{
    protected ?OAuthService $oauthService = null;
    protected OAuthManager $oauthManager;

    public function __construct(?OAuthService $oauthService = null)
    {
        $this->oauthService = $oauthService;
        $this->oauthManager = app(OAuthManager::class);
        
        if ($oauthService) {
            $this->initializeService();
        }
    }

    public function setOAuthService(OAuthService $oauthService): self
    {
        $this->oauthService = $oauthService;
        $this->initializeService();
        return $this;
    }

    public function getOAuthService(): ?OAuthService
    {
        return $this->oauthService;
    }

    /**
     * Initialize the service-specific client/connection
     * This method should be implemented by each concrete explorer
     */
    abstract protected function initializeService(): void;

    /**
     * Ensure we have a valid OAuth token
     * 
     * @throws \Exception If OAuth service is not configured or token is invalid
     */
    protected function ensureValidToken(): void
    {
        if (!$this->oauthService) {
            throw new \Exception('OAuth service not configured');
        }

        if (!$this->oauthService->is_active) {
            throw new \Exception('OAuth service is not active');
        }

        if (!$this->oauthService->access_token) {
            throw new \Exception('No access token available');
        }

        // Refresh token if needed
        if ($this->oauthService->needsRefresh()) {
            $refreshed = $this->oauthManager->refreshTokenIfNeeded($this->oauthService);
            if (!$refreshed) {
                throw new \Exception('Failed to refresh access token');
            }
        }
    }

    /**
     * Format file data to a standard structure
     * 
     * @param mixed $file Raw file data from the service
     * @return array Standardized file data
     */
    abstract protected function formatFileData($file): array;

    /**
     * Get the standard file data structure
     * 
     * @param string $id File ID
     * @param string $name File name
     * @param string $mimeType File mime type
     * @param bool $isFolder Whether this is a folder
     * @param array $additional Additional service-specific data
     * @return array Standardized file structure
     */
    protected function getStandardFileStructure(
        string $id,
        string $name,
        string $mimeType,
        bool $isFolder = false,
        array $additional = []
    ): array {
        return array_merge([
            'id' => $id,
            'name' => $name,
            'mimeType' => $mimeType,
            'isFolder' => $isFolder,
            'size' => $additional['size'] ?? null,
            'modifiedTime' => $additional['modifiedTime'] ?? null,
            'webViewLink' => $additional['webViewLink'] ?? null,
            'webContentLink' => $additional['webContentLink'] ?? null,
            'iconLink' => $additional['iconLink'] ?? null,
            'parents' => $additional['parents'] ?? [],
            'serviceType' => $this->getServiceType(),
        ], $additional);
    }

    public function testConnection(): bool
    {
        try {
            $this->ensureValidToken();
            return $this->performConnectionTest();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Perform service-specific connection test
     * This method should be implemented by each concrete explorer
     */
    abstract protected function performConnectionTest(): bool;

    /**
     * Get supported file types for this explorer
     * Override this method in concrete implementations to specify supported types
     */
    public function getSupportedMimeTypes(): array
    {
        return []; // Default: support all types
    }

    public function supportsMimeType(string $mimeType): bool
    {
        $supportedTypes = $this->getSupportedMimeTypes();
        
        // If no specific types are defined, support all
        if (empty($supportedTypes)) {
            return true;
        }

        return in_array($mimeType, $supportedTypes);
    }

    /**
     * Get the folder mime type for this service
     * Override in concrete implementations if different
     */
    protected function getFolderMimeType(): string
    {
        return 'application/vnd.google-apps.folder';
    }

    /**
     * Check if a file is a folder based on its mime type
     */
    protected function isFolder(string $mimeType): bool
    {
        return $mimeType === $this->getFolderMimeType();
    }
}
