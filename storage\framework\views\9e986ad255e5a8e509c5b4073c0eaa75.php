<div class="space-y-4">
    
    <!--[if BLOCK]><![endif]--><?php if(!$oauthServiceId): ?>
        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-sm text-yellow-800">
                Please select a service first to browse files.
            </p>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($error): ?>
        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-800"><?php echo e($error); ?></p>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($oauthServiceId && $serviceType): ?>
        <div class="flex items-center text-sm text-gray-600 mb-2">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
            </svg>
            Browsing <?php echo e($this->getServiceTypeDisplayName()); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($oauthServiceId): ?>
        <div class="flex gap-2">
            <div class="flex-1">
                <input 
                    type="text" 
                    wire:model.defer="searchQuery"
                    wire:keydown.enter="search"
                    placeholder="Search files..."
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
            </div>
            <button 
                wire:click="search"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
                Search
            </button>
            <!--[if BLOCK]><![endif]--><?php if($searchQuery): ?>
                <button 
                    wire:click="clearSearch"
                    class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                    Clear
                </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($oauthServiceId && count($breadcrumb) > 0): ?>
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $breadcrumb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $crumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="flex items-center">
                        <!--[if BLOCK]><![endif]--><?php if($index > 0): ?>
                            <svg class="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <button 
                            wire:click="openFolder('<?php echo e($crumb['id']); ?>')"
                            class="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
                        >
                            <?php echo e($crumb['name']); ?>

                        </button>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </ol>
        </nav>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($isLoading): ?>
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Loading...</span>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($oauthServiceId && !$isLoading): ?>
        <div class="border border-gray-200 rounded-lg overflow-hidden">
            <!--[if BLOCK]><![endif]--><?php if(count($files) > 0): ?>
                <div class="divide-y divide-gray-200">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div 
                            <?php if($file['isFolder']): ?>
                                wire:click="openFolder('<?php echo e($file['id']); ?>')"
                            <?php else: ?>
                                wire:click="selectFile('<?php echo e($file['id']); ?>', '<?php echo e(addslashes($file['name'])); ?>', '<?php echo e($file['webViewLink'] ?? $file['webContentLink'] ?? ''); ?>', '<?php echo e($file['mimeType']); ?>')"
                            <?php endif; ?>
                            class="flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-150 <?php echo e($selectedFile && $selectedFile['id'] === $file['id'] ? 'bg-blue-50 border-l-4 border-blue-500' : ''); ?>"
                        >
                            
                            <div class="flex-shrink-0 mr-3">
                                <!--[if BLOCK]><![endif]--><?php if($file['iconLink'] ?? false): ?>
                                    <img src="<?php echo e($file['iconLink']); ?>" alt="File icon" class="w-6 h-6">
                                <?php else: ?>
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <!--[if BLOCK]><![endif]--><?php if($file['isFolder']): ?>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
                                        <?php else: ?>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </svg>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        <?php echo e($file['name']); ?>

                                        <!--[if BLOCK]><![endif]--><?php if($file['isFolder']): ?>
                                            <span class="text-xs text-gray-500 ml-1">(folder)</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </p>
                                    <!--[if BLOCK]><![endif]--><?php if(!$file['isFolder'] && isset($file['sizeFormatted'])): ?>
                                        <p class="text-xs text-gray-500 ml-2">
                                            <?php echo e($file['sizeFormatted']); ?>

                                        </p>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <!--[if BLOCK]><![endif]--><?php if($file['modifiedTime'] ?? false): ?>
                                    <p class="text-xs text-gray-500">
                                        Modified: <?php echo e(\Carbon\Carbon::parse($file['modifiedTime'])->format('M j, Y g:i A')); ?>

                                    </p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if(!empty($acceptedMimeTypes) && !in_array($file['mimeType'], $acceptedMimeTypes) && !$file['isFolder']): ?>
                                    <p class="text-xs text-red-500">
                                        File type not accepted
                                    </p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            
                            <!--[if BLOCK]><![endif]--><?php if($selectedFile && $selectedFile['id'] === $file['id']): ?>
                                <div class="flex-shrink-0 ml-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php else: ?>
                <div class="p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No files found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        <!--[if BLOCK]><![endif]--><?php if($searchQuery): ?>
                            No files match your search criteria.
                        <?php else: ?>
                            This folder is empty.
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </p>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <!--[if BLOCK]><![endif]--><?php if($selectedFile): ?>
        <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-green-800">Selected: <?php echo e($selectedFile['name']); ?></p>
                    <p class="text-xs text-green-600"><?php echo e($selectedFileUrl); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\vendor\lbcdev\oauth-file-explorer\src/../resources/views/livewire/oauth-file-explorer.blade.php ENDPATH**/ ?>