<?php

namespace LBCDev\OAuthFileExplorer\Services\Explorers;

use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;
use Spatie\Dropbox\Client as DropboxClient;

class DropboxExplorer extends AbstractFileExplorer
{
    protected ?DropboxClient $dropboxClient = null;

    public function getServiceType(): string
    {
        return 'dropbox';
    }

    protected function initializeService(): void
    {
        $this->ensureValidToken();
        
        $this->dropboxClient = new DropboxClient($this->oauthService->access_token);
    }

    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        $this->ensureValidToken();
        
        if (!$this->dropboxClient) {
            $this->initializeService();
        }

        try {
            // Convert 'root' to empty string for Dropbox API
            $path = $folderId === 'root' ? '' : $folderId;
            
            $response = $this->dropboxClient->listFolder($path);
            $entries = $response['entries'] ?? [];

            $formattedFiles = [];
            foreach ($entries as $entry) {
                $formattedFiles[] = $this->formatFileData($entry);
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $response['has_more'] ?? false ? ($response['cursor'] ?? null) : null
            ];
        } catch (\Exception $e) {
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    public function getFile(string $fileId): array
    {
        $this->ensureValidToken();
        
        if (!$this->dropboxClient) {
            $this->initializeService();
        }

        try {
            $metadata = $this->dropboxClient->getMetadata($fileId);
            return $this->formatFileData($metadata);
        } catch (\Exception $e) {
            throw new \Exception('Error getting file: ' . $e->getMessage());
        }
    }

    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        $this->ensureValidToken();
        
        if (!$this->dropboxClient) {
            $this->initializeService();
        }

        try {
            // Convert 'root' to empty string for Dropbox API
            $path = $folderId === 'root' ? '' : $folderId;
            
            $response = $this->dropboxClient->search($path, $query);
            $matches = $response['matches'] ?? [];

            $formattedFiles = [];
            foreach ($matches as $match) {
                if (isset($match['metadata'])) {
                    $formattedFiles[] = $this->formatFileData($match['metadata']);
                }
            }

            return $formattedFiles;
        } catch (\Exception $e) {
            throw new \Exception('Error searching files: ' . $e->getMessage());
        }
    }

    public function getBreadcrumb(string $folderId): array
    {
        if ($folderId === 'root' || $folderId === '') {
            return [['id' => 'root', 'name' => 'Dropbox']];
        }

        try {
            $breadcrumb = [];
            $currentPath = $folderId;

            // Build breadcrumb by parsing the path
            $pathParts = explode('/', trim($currentPath, '/'));
            $currentFullPath = '';

            foreach ($pathParts as $part) {
                if (!empty($part)) {
                    $currentFullPath .= '/' . $part;
                    $breadcrumb[] = [
                        'id' => $currentFullPath,
                        'name' => $part
                    ];
                }
            }

            // Add root at the beginning
            array_unshift($breadcrumb, ['id' => 'root', 'name' => 'Dropbox']);

            return $breadcrumb;
        } catch (\Exception) {
            return [['id' => 'root', 'name' => 'Dropbox']];
        }
    }

    protected function performConnectionTest(): bool
    {
        try {
            if (!$this->dropboxClient) {
                $this->initializeService();
            }

            // Test connection by getting account info
            $this->dropboxClient->getAccountInfo();
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    protected function formatFileData($file): array
    {
        if (!is_array($file)) {
            throw new \InvalidArgumentException('Expected array for Dropbox file data');
        }

        $isFolder = ($file['.tag'] ?? '') === 'folder';
        $name = $file['name'] ?? 'Unknown';
        $id = $file['path_lower'] ?? $file['id'] ?? '';

        // Generate web links (Dropbox doesn't provide direct web links in metadata)
        $webViewLink = null;
        $webContentLink = null;
        
        if (!$isFolder) {
            // For files, we could generate sharing links, but this requires additional API calls
            // For now, we'll use the path as identifier
            $webViewLink = "https://dropbox.com/home" . $id;
        }

        return $this->getStandardFileStructure(
            $id,
            $name,
            $isFolder ? $this->getFolderMimeType() : ($file['mime_type'] ?? 'application/octet-stream'),
            $isFolder,
            [
                'size' => $file['size'] ?? 0,
                'sizeFormatted' => isset($file['size']) ? $this->formatFileSize($file['size']) : '-',
                'modifiedTime' => $file['server_modified'] ?? $file['client_modified'] ?? null,
                'webViewLink' => $webViewLink,
                'webContentLink' => $webContentLink,
                'iconLink' => null, // Dropbox doesn't provide icon links
                'parents' => $this->getParentPath($id),
                'downloadUrl' => !$isFolder ? $webViewLink : null
            ]
        );
    }

    protected function getFolderMimeType(): string
    {
        return 'application/vnd.dropbox.folder';
    }

    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);

        $size = $bytes / pow(1024, $power);
        $formattedSize = round($size, 2);

        return $formattedSize . ' ' . $units[$power];
    }

    private function getParentPath(string $path): array
    {
        if ($path === '' || $path === '/') {
            return [];
        }

        $parentPath = dirname($path);
        return $parentPath === '/' ? [] : [$parentPath];
    }

    public function getSupportedMimeTypes(): array
    {
        return [
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            
            // Images
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/svg+xml',
            
            // Videos
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv',
            
            // Audio
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
            'audio/flac',
            'audio/wma',
            
            // Archives
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            
            // Folders
            'application/vnd.dropbox.folder'
        ];
    }
}
