<?php

namespace App\Filament\Resources\OAuthServiceResource\Pages;

use Filament\Actions;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\OAuthServiceResource;

class EditOAuthService extends EditRecord
{
    protected static string $resource = OAuthServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('authorize')
                ->label('Authorize Service')
                ->icon('heroicon-m-key')
                ->color('success')
                ->visible(fn() => !$this->record->access_token || $this->record->isTokenExpired())
                ->url(fn() => route('oauth-manager.authorize', $this->record)),

            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // comprobar si $data['credentials'] es vacio y si lo es llenar con los valores de la configuración
        if (empty($data['credentials'])) {
            $data['credentials'] = config('oauth-manager.services.' . $data['service_type'] . '.fields');
        }

        $record->update($data);

        return $record;
    }
}
